import { AudioGenerationRequest, PodcastSegment, VoiceSettings } from '../types';
import { AUDIO_MODELS, AUDIO_MODEL_CONFIGS } from '../constants';

// ElevenLabs API Service
class ElevenLabsService {
  private apiKey: string;
  private baseUrl = 'https://api.elevenlabs.io/v1';

  constructor() {
    this.apiKey = import.meta.env.VITE_ELEVENLABS_API_KEY || '';
  }

  async generateAudio(request: AudioGenerationRequest): Promise<string> {
    if (!this.apiKey) {
      throw new Error('ElevenLabs API key not configured');
    }

    try {
      const response = await fetch(`${this.baseUrl}/text-to-speech/${request.voiceId}`, {
        method: 'POST',
        headers: {
          'Accept': 'audio/mpeg',
          'Content-Type': 'application/json',
          'xi-api-key': this.apiKey,
        },
        body: JSON.stringify({
          text: request.text,
          model_id: request.model,
          voice_settings: {
            stability: request.settings.stability || 0.75,
            similarity_boost: request.settings.clarity || 0.75,
            style: request.settings.style || 0.0,
            use_speaker_boost: request.settings.speakerBoost || true,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.statusText}`);
      }

      const audioBlob = await response.blob();
      return URL.createObjectURL(audioBlob);

    } catch (error) {
      console.error('ElevenLabs generation failed:', error);
      throw new Error(`ElevenLabs audio generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getVoices(): Promise<any[]> {
    if (!this.apiKey) return [];

    try {
      const response = await fetch(`${this.baseUrl}/voices`, {
        headers: {
          'xi-api-key': this.apiKey,
        },
      });

      if (!response.ok) return [];

      const data = await response.json();
      return data.voices || [];

    } catch (error) {
      console.error('Failed to fetch ElevenLabs voices:', error);
      return [];
    }
  }
}

// Google Cloud TTS Service
class GoogleTTSService {
  private apiKey: string;
  private baseUrl = 'https://texttospeech.googleapis.com/v1';

  constructor() {
    this.apiKey = import.meta.env.VITE_GOOGLE_CLOUD_API_KEY || '';
  }

  async generateAudio(request: AudioGenerationRequest): Promise<string> {
    if (!this.apiKey) {
      throw new Error('Google Cloud API key not configured');
    }

    try {
      const response = await fetch(`${this.baseUrl}/text:synthesize?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: { text: request.text },
          voice: {
            languageCode: 'en-US',
            name: request.voiceId,
          },
          audioConfig: {
            audioEncoding: 'MP3',
            speakingRate: request.settings.speed || 1.0,
            pitch: request.settings.pitch || 0.0,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`Google TTS API error: ${response.statusText}`);
      }

      const data = await response.json();
      const audioBlob = new Blob([Uint8Array.from(atob(data.audioContent), c => c.charCodeAt(0))], {
        type: 'audio/mpeg',
      });

      return URL.createObjectURL(audioBlob);

    } catch (error) {
      console.error('Google TTS generation failed:', error);
      throw new Error(`Google TTS audio generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// OpenAI TTS Service
class OpenAITTSService {
  private apiKey: string;
  private baseUrl = 'https://api.openai.com/v1';

  constructor() {
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY || '';
  }

  async generateAudio(request: AudioGenerationRequest): Promise<string> {
    if (!this.apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    try {
      const response = await fetch(`${this.baseUrl}/audio/speech`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: request.model,
          input: request.text,
          voice: request.voiceId,
          speed: request.settings.speed || 1.0,
          response_format: request.format || 'mp3',
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI TTS API error: ${response.statusText}`);
      }

      const audioBlob = await response.blob();
      return URL.createObjectURL(audioBlob);

    } catch (error) {
      console.error('OpenAI TTS generation failed:', error);
      throw new Error(`OpenAI TTS audio generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Main Audio Generation Service
class AudioGenerationService {
  private elevenLabs: ElevenLabsService;
  private googleTTS: GoogleTTSService;
  private openAI: OpenAITTSService;

  constructor() {
    this.elevenLabs = new ElevenLabsService();
    this.googleTTS = new GoogleTTSService();
    this.openAI = new OpenAITTSService();
  }

  async generatePodcastAudio(
    segments: PodcastSegment[],
    audioModel: string,
    voiceSettings: VoiceSettings
  ): Promise<string[]> {
    const audioUrls: string[] = [];

    for (const segment of segments) {
      const voiceId = segment.speaker === 'host' 
        ? voiceSettings.hostVoice 
        : voiceSettings.guestVoice;

      const request: AudioGenerationRequest = {
        text: segment.text,
        voiceId,
        model: audioModel,
        settings: {
          speed: voiceSettings.speed,
          pitch: voiceSettings.pitch,
          stability: voiceSettings.stability,
          clarity: voiceSettings.clarity,
          style: voiceSettings.style,
          speakerBoost: voiceSettings.speakerBoost,
        },
        quality: 'high',
      };

      try {
        const audioUrl = await this.generateSegmentAudio(request, audioModel);
        audioUrls.push(audioUrl);
      } catch (error) {
        console.error(`Failed to generate audio for segment ${segment.id}:`, error);
        // Continue with other segments
        audioUrls.push('');
      }
    }

    return audioUrls;
  }

  private async generateSegmentAudio(
    request: AudioGenerationRequest,
    audioModel: string
  ): Promise<string> {
    const modelConfig = AUDIO_MODEL_CONFIGS[audioModel as keyof typeof AUDIO_MODEL_CONFIGS];
    
    if (!modelConfig) {
      throw new Error(`Unsupported audio model: ${audioModel}`);
    }

    switch (modelConfig.provider) {
      case 'elevenlabs':
        return await this.elevenLabs.generateAudio(request);
      case 'google':
        return await this.googleTTS.generateAudio(request);
      case 'openai':
        return await this.openAI.generateAudio(request);
      default:
        throw new Error(`Unsupported audio provider: ${modelConfig.provider}`);
    }
  }

  async combineAudioSegments(audioUrls: string[]): Promise<string> {
    // In a real implementation, this would combine multiple audio files
    // For now, return the first valid audio URL as a placeholder
    const validUrls = audioUrls.filter(url => url);
    return validUrls[0] || '';
  }

  async addBackgroundMusic(
    audioUrl: string,
    musicType: string,
    volume: number
  ): Promise<string> {
    // In a real implementation, this would mix background music with the audio
    // For now, return the original audio URL
    console.log('Adding background music:', { musicType, volume });
    return audioUrl;
  }

  validateAudioModel(audioModel: string): boolean {
    return audioModel in AUDIO_MODEL_CONFIGS;
  }

  getModelCapabilities(audioModel: string) {
    const config = AUDIO_MODEL_CONFIGS[audioModel as keyof typeof AUDIO_MODEL_CONFIGS];
    return config || null;
  }

  estimateAudioGenerationTime(
    segments: PodcastSegment[],
    audioModel: string
  ): number {
    const totalCharacters = segments.reduce((sum, segment) => sum + segment.text.length, 0);
    const modelConfig = AUDIO_MODEL_CONFIGS[audioModel as keyof typeof AUDIO_MODEL_CONFIGS];
    
    if (!modelConfig) return 60; // Default 1 minute

    // Estimate based on provider and model
    const baseTimePerChar = modelConfig.provider === 'elevenlabs' ? 0.1 : 0.05;
    return Math.max(30, totalCharacters * baseTimePerChar);
  }

  async getAvailableVoices(provider: string): Promise<any[]> {
    switch (provider) {
      case 'elevenlabs':
        return await this.elevenLabs.getVoices();
      default:
        return [];
    }
  }
}

export const audioGenerationService = new AudioGenerationService();
