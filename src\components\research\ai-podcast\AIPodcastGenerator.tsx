import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  PodcastGeneratorState, 
  GenerationStep, 
  ContentSource,
  GeneratedPodcast,
  PodcastStatus
} from './types';
import { DEFAULT_GENERATION_SETTINGS } from './constants';
import { ContentInput } from './components/ContentInput';
import { GenerationProgress } from './components/GenerationProgress';
import { PodcastPlayer } from './components/PodcastPlayer';
import { PodcastHistory } from './components/PodcastHistory';
import { SettingsPanel } from './components/SettingsPanel';
import { podcastGenerationService } from './services/podcast-generation.service';
import { supabasePodcastService } from './services/supabase-podcast.service';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Mic, 
  Upload, 
  Settings, 
  History, 
  Play,
  Download,
  Share2,
  RefreshCw
} from 'lucide-react';

export function AIPodcastGenerator() {
  const [state, setState] = useState<PodcastGeneratorState>({
    sources: [],
    isGenerating: false,
    generationStep: GenerationStep.UPLOAD,
    settings: DEFAULT_GENERATION_SETTINGS,
  });

  const [activeTab, setActiveTab] = useState('create');
  const [podcastHistory, setPodcastHistory] = useState<GeneratedPodcast[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);

  // Load podcast history on component mount
  useEffect(() => {
    loadPodcastHistory();
  }, []);

  const loadPodcastHistory = async () => {
    setIsLoadingHistory(true);
    try {
      const history = await supabasePodcastService.getUserPodcasts();
      setPodcastHistory(history);
    } catch (error) {
      console.error('Failed to load podcast history:', error);
      toast.error('Failed to load podcast history');
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const handleAddSource = (source: ContentSource) => {
    setState(prev => ({
      ...prev,
      sources: [...prev.sources, source],
    }));
    toast.success('Content source added successfully');
  };

  const handleRemoveSource = (sourceId: string) => {
    setState(prev => ({
      ...prev,
      sources: prev.sources.filter(s => s.id !== sourceId),
    }));
  };

  const handleUpdateSettings = (newSettings: Partial<typeof state.settings>) => {
    setState(prev => ({
      ...prev,
      settings: { ...prev.settings, ...newSettings },
    }));
  };

  const handleGeneratePodcast = async () => {
    if (state.sources.length === 0) {
      toast.error('Please add at least one content source');
      return;
    }

    setState(prev => ({
      ...prev,
      isGenerating: true,
      generationStep: GenerationStep.OUTLINE,
      error: undefined,
    }));

    try {
      // Step 1: Generate outline
      setState(prev => ({ ...prev, generationStep: GenerationStep.OUTLINE }));
      const outline = await podcastGenerationService.generateOutline(
        state.sources,
        state.settings
      );

      // Step 2: Generate script
      setState(prev => ({ ...prev, generationStep: GenerationStep.SCRIPT }));
      const script = await podcastGenerationService.generateScript(
        outline,
        state.sources,
        state.settings
      );

      // Step 3: Generate audio (placeholder for now)
      setState(prev => ({ ...prev, generationStep: GenerationStep.AUDIO }));
      // Audio generation would be implemented here
      
      // Create the generated podcast object
      const generatedPodcast: GeneratedPodcast = {
        id: `podcast_${Date.now()}`,
        metadata: {
          title: outline.title,
          description: outline.outline.substring(0, 200) + '...',
          language: state.settings.language,
          tags: outline.keyPoints,
        },
        script: {
          id: `script_${Date.now()}`,
          segments: script.script,
          wordCount: script.metadata.wordCount,
          totalDuration: script.metadata.estimatedDuration,
        },
        status: PodcastStatus.COMPLETED,
        createdAt: new Date(),
        updatedAt: new Date(),
        sources: state.sources,
        generationSettings: state.settings,
      };

      // Save to Supabase
      await supabasePodcastService.savePodcast(generatedPodcast);

      setState(prev => ({
        ...prev,
        currentPodcast: generatedPodcast,
        generationStep: GenerationStep.COMPLETE,
        isGenerating: false,
      }));

      // Refresh history
      await loadPodcastHistory();
      
      toast.success('Podcast generated successfully!');
      setActiveTab('player');

    } catch (error) {
      console.error('Podcast generation failed:', error);
      setState(prev => ({
        ...prev,
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Generation failed',
      }));
      toast.error('Failed to generate podcast');
    }
  };

  const handleResetGeneration = () => {
    setState(prev => ({
      ...prev,
      sources: [],
      currentPodcast: undefined,
      isGenerating: false,
      generationStep: GenerationStep.UPLOAD,
      error: undefined,
    }));
    setActiveTab('create');
  };

  const handleLoadPodcast = (podcast: GeneratedPodcast) => {
    setState(prev => ({
      ...prev,
      currentPodcast: podcast,
    }));
    setActiveTab('player');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl text-white">
              <Mic className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">AI Podcast Generator</h1>
              <p className="text-gray-600">Transform your research content into engaging podcasts</p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="create" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Create
            </TabsTrigger>
            <TabsTrigger value="player" className="flex items-center gap-2">
              <Play className="h-4 w-4" />
              Player
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              History
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          {/* Create Tab */}
          <TabsContent value="create" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Content Input */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Upload className="h-5 w-5" />
                      Content Sources
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ContentInput
                      sources={state.sources}
                      onAddSource={handleAddSource}
                      onRemoveSource={handleRemoveSource}
                      disabled={state.isGenerating}
                    />
                  </CardContent>
                </Card>
              </div>

              {/* Generation Panel */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Mic className="h-5 w-5" />
                      Generate Podcast
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {state.isGenerating ? (
                      <GenerationProgress 
                        step={state.generationStep}
                        error={state.error}
                      />
                    ) : (
                      <div className="space-y-4">
                        <div className="text-sm text-gray-600">
                          {state.sources.length} content source(s) added
                        </div>
                        <Button
                          onClick={handleGeneratePodcast}
                          disabled={state.sources.length === 0}
                          className="w-full"
                          size="lg"
                        >
                          <Mic className="h-4 w-4 mr-2" />
                          Generate Podcast
                        </Button>
                        {state.sources.length > 0 && (
                          <Button
                            onClick={handleResetGeneration}
                            variant="outline"
                            className="w-full"
                          >
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Reset
                          </Button>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Player Tab */}
          <TabsContent value="player">
            {state.currentPodcast ? (
              <PodcastPlayer podcast={state.currentPodcast} />
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Play className="h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Podcast Selected</h3>
                  <p className="text-gray-600 text-center mb-4">
                    Generate a new podcast or select one from your history to play
                  </p>
                  <Button onClick={() => setActiveTab('create')}>
                    Create New Podcast
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history">
            <PodcastHistory
              podcasts={podcastHistory}
              isLoading={isLoadingHistory}
              onLoadPodcast={handleLoadPodcast}
              onRefresh={loadPodcastHistory}
            />
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings">
            <SettingsPanel
              settings={state.settings}
              onUpdateSettings={handleUpdateSettings}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
