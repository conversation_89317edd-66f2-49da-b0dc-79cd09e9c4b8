import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  PodcastGeneratorState, 
  GenerationStep, 
  ContentSource,
  GeneratedPodcast,
  PodcastStatus
} from './types';
import { DEFAULT_GENERATION_SETTINGS } from './constants';
import { ContentInput } from './components/ContentInput';
import { GenerationProgress } from './components/GenerationProgress';
import { PodcastPlayer } from './components/PodcastPlayer';
import { PodcastHistory } from './components/PodcastHistory';
import { SettingsPanel } from './components/SettingsPanel';
import { podcastGenerationService } from './services/podcast-generation.service';
import { supabasePodcastService } from './services/supabase-podcast.service';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Mic,
  Upload,
  Settings,
  History,
  Play,
  Download,
  Share2,
  RefreshCw,
  Sparkles,
  AudioWaveform,
  Clock,
  Globe
} from 'lucide-react';

export function AIPodcastGenerator() {
  const [state, setState] = useState<PodcastGeneratorState>({
    sources: [],
    isGenerating: false,
    generationStep: GenerationStep.UPLOAD,
    settings: DEFAULT_GENERATION_SETTINGS,
  });

  const [activeTab, setActiveTab] = useState('create');
  const [podcastHistory, setPodcastHistory] = useState<GeneratedPodcast[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);

  // Load podcast history on component mount
  useEffect(() => {
    loadPodcastHistory();
  }, []);

  const loadPodcastHistory = async () => {
    setIsLoadingHistory(true);
    try {
      const history = await supabasePodcastService.getUserPodcasts();
      setPodcastHistory(history);
    } catch (error) {
      console.error('Failed to load podcast history:', error);
      toast.error('Failed to load podcast history');
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const handleAddSource = (source: ContentSource) => {
    setState(prev => ({
      ...prev,
      sources: [...prev.sources, source],
    }));
    toast.success('Content source added successfully');
  };

  const handleRemoveSource = (sourceId: string) => {
    setState(prev => ({
      ...prev,
      sources: prev.sources.filter(s => s.id !== sourceId),
    }));
  };

  const handleUpdateSettings = (newSettings: Partial<typeof state.settings>) => {
    setState(prev => ({
      ...prev,
      settings: { ...prev.settings, ...newSettings },
    }));
  };

  const handleGeneratePodcast = async () => {
    if (state.sources.length === 0) {
      toast.error('Please add at least one content source');
      return;
    }

    setState(prev => ({
      ...prev,
      isGenerating: true,
      generationStep: GenerationStep.OUTLINE,
      error: undefined,
    }));

    try {
      // Step 1: Generate outline
      setState(prev => ({ ...prev, generationStep: GenerationStep.OUTLINE }));
      const outline = await podcastGenerationService.generateOutline(
        state.sources,
        state.settings
      );

      // Step 2: Generate script
      setState(prev => ({ ...prev, generationStep: GenerationStep.SCRIPT }));
      const script = await podcastGenerationService.generateScript(
        outline,
        state.sources,
        state.settings
      );

      // Step 3: Generate audio (placeholder for now)
      setState(prev => ({ ...prev, generationStep: GenerationStep.AUDIO }));
      // Audio generation would be implemented here
      
      // Create the generated podcast object
      const generatedPodcast: GeneratedPodcast = {
        id: `podcast_${Date.now()}`,
        metadata: {
          title: outline.title,
          description: outline.outline.substring(0, 200) + '...',
          language: state.settings.language,
          tags: outline.keyPoints,
        },
        script: {
          id: `script_${Date.now()}`,
          segments: script.script,
          wordCount: script.metadata.wordCount,
          totalDuration: script.metadata.estimatedDuration,
        },
        status: PodcastStatus.COMPLETED,
        createdAt: new Date(),
        updatedAt: new Date(),
        sources: state.sources,
        generationSettings: state.settings,
      };

      // Save to Supabase
      await supabasePodcastService.savePodcast(generatedPodcast);

      setState(prev => ({
        ...prev,
        currentPodcast: generatedPodcast,
        generationStep: GenerationStep.COMPLETE,
        isGenerating: false,
      }));

      // Refresh history
      await loadPodcastHistory();
      
      toast.success('Podcast generated successfully!');
      setActiveTab('player');

    } catch (error) {
      console.error('Podcast generation failed:', error);
      setState(prev => ({
        ...prev,
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Generation failed',
      }));
      toast.error('Failed to generate podcast');
    }
  };

  const handleResetGeneration = () => {
    setState(prev => ({
      ...prev,
      sources: [],
      currentPodcast: undefined,
      isGenerating: false,
      generationStep: GenerationStep.UPLOAD,
      error: undefined,
    }));
    setActiveTab('create');
  };

  const handleLoadPodcast = (podcast: GeneratedPodcast) => {
    setState(prev => ({
      ...prev,
      currentPodcast: podcast,
    }));
    setActiveTab('player');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Enhanced Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 rounded-2xl text-white shadow-lg">
                <Mic className="h-10 w-10" />
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  AI Podcast Generator
                </h1>
                <p className="text-lg text-gray-600 mt-1">
                  Transform your research content into professional podcasts with AI
                </p>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="hidden lg:flex items-center gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{state.sources.length}</div>
                <div className="text-sm text-gray-600">Sources</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{podcastHistory.length}</div>
                <div className="text-sm text-gray-600">Podcasts</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-cyan-600">
                  {state.settings.audioModel.includes('elevenlabs') ? 'Premium' : 'Standard'}
                </div>
                <div className="text-sm text-gray-600">Quality</div>
              </div>
            </div>
          </div>

          {/* Feature Highlights */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="flex items-center gap-3 p-4 bg-white rounded-xl shadow-sm border border-purple-100">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Sparkles className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <div className="font-semibold text-gray-900">AI-Powered</div>
                <div className="text-sm text-gray-600">Advanced content analysis</div>
              </div>
            </div>
            <div className="flex items-center gap-3 p-4 bg-white rounded-xl shadow-sm border border-blue-100">
              <div className="p-2 bg-blue-100 rounded-lg">
                <AudioWaveform className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <div className="font-semibold text-gray-900">Premium Audio</div>
                <div className="text-sm text-gray-600">ElevenLabs & Google TTS</div>
              </div>
            </div>
            <div className="flex items-center gap-3 p-4 bg-white rounded-xl shadow-sm border border-green-100">
              <div className="p-2 bg-green-100 rounded-lg">
                <Clock className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <div className="font-semibold text-gray-900">Fast Generation</div>
                <div className="text-sm text-gray-600">Minutes, not hours</div>
              </div>
            </div>
            <div className="flex items-center gap-3 p-4 bg-white rounded-xl shadow-sm border border-orange-100">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Globe className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <div className="font-semibold text-gray-900">Multi-Language</div>
                <div className="text-sm text-gray-600">11+ languages supported</div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
          <div className="flex justify-center">
            <TabsList className="grid w-full max-w-2xl grid-cols-4 h-14 bg-white shadow-lg rounded-2xl border border-gray-200">
              <TabsTrigger
                value="create"
                className="flex items-center gap-2 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-blue-500 data-[state=active]:text-white"
              >
                <Upload className="h-5 w-5" />
                <span className="hidden sm:inline">Create</span>
              </TabsTrigger>
              <TabsTrigger
                value="player"
                className="flex items-center gap-2 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-blue-500 data-[state=active]:text-white"
              >
                <Play className="h-5 w-5" />
                <span className="hidden sm:inline">Player</span>
              </TabsTrigger>
              <TabsTrigger
                value="history"
                className="flex items-center gap-2 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-cyan-500 data-[state=active]:text-white"
              >
                <History className="h-5 w-5" />
                <span className="hidden sm:inline">History</span>
              </TabsTrigger>
              <TabsTrigger
                value="settings"
                className="flex items-center gap-2 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white"
              >
                <Settings className="h-5 w-5" />
                <span className="hidden sm:inline">Settings</span>
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Create Tab */}
          <TabsContent value="create" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Content Input */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Upload className="h-5 w-5" />
                      Content Sources
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ContentInput
                      sources={state.sources}
                      onAddSource={handleAddSource}
                      onRemoveSource={handleRemoveSource}
                      disabled={state.isGenerating}
                    />
                  </CardContent>
                </Card>
              </div>

              {/* Enhanced Generation Panel */}
              <div>
                <Card className="border-2 border-gradient-to-r from-purple-200 to-blue-200 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-purple-50 to-blue-50">
                    <CardTitle className="flex items-center gap-2">
                      <div className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg text-white">
                        <Mic className="h-5 w-5" />
                      </div>
                      Generate Podcast
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6 p-6">
                    {state.isGenerating ? (
                      <GenerationProgress
                        step={state.generationStep}
                        error={state.error}
                      />
                    ) : (
                      <div className="space-y-6">
                        {/* Current Settings Summary */}
                        <div className="p-4 bg-gray-50 rounded-xl border">
                          <h4 className="font-medium text-gray-900 mb-3">Current Settings</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Content Model:</span>
                              <span className="font-medium">{state.settings.contentModel.split('-').pop()}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Audio Model:</span>
                              <span className="font-medium">{state.settings.audioModel.includes('elevenlabs') ? 'ElevenLabs' : 'Google'}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Style:</span>
                              <span className="font-medium capitalize">{state.settings.style}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Length:</span>
                              <span className="font-medium capitalize">{state.settings.length}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Quality:</span>
                              <span className="font-medium capitalize">{state.settings.audioQuality}</span>
                            </div>
                          </div>
                        </div>

                        {/* Sources Summary */}
                        <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                          <div className="flex items-center gap-2 mb-2">
                            <Upload className="h-4 w-4 text-blue-600" />
                            <span className="font-medium text-blue-900">
                              {state.sources.length} Content Source{state.sources.length !== 1 ? 's' : ''} Added
                            </span>
                          </div>
                          {state.sources.length > 0 && (
                            <div className="text-sm text-blue-700">
                              Total characters: {state.sources.reduce((sum, s) => sum + s.content.length, 0).toLocaleString()}
                            </div>
                          )}
                        </div>

                        {/* Generation Button */}
                        <Button
                          onClick={handleGeneratePodcast}
                          disabled={state.sources.length === 0}
                          className="w-full h-14 text-lg bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg"
                          size="lg"
                        >
                          <Sparkles className="h-6 w-6 mr-3" />
                          Generate AI Podcast
                        </Button>

                        {state.sources.length > 0 && (
                          <Button
                            onClick={handleResetGeneration}
                            variant="outline"
                            className="w-full border-2 border-gray-300 hover:border-gray-400"
                          >
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Reset & Start Over
                          </Button>
                        )}

                        {/* Quick Tips */}
                        <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                          <h4 className="font-medium text-yellow-900 mb-2">💡 Pro Tips</h4>
                          <ul className="text-sm text-yellow-800 space-y-1">
                            <li>• Use multiple sources for richer content</li>
                            <li>• ElevenLabs provides the highest quality voices</li>
                            <li>• Longer content generates better discussions</li>
                            <li>• Check Settings tab for advanced options</li>
                          </ul>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Player Tab */}
          <TabsContent value="player">
            {state.currentPodcast ? (
              <PodcastPlayer podcast={state.currentPodcast} />
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Play className="h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Podcast Selected</h3>
                  <p className="text-gray-600 text-center mb-4">
                    Generate a new podcast or select one from your history to play
                  </p>
                  <Button onClick={() => setActiveTab('create')}>
                    Create New Podcast
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history">
            <PodcastHistory
              podcasts={podcastHistory}
              isLoading={isLoadingHistory}
              onLoadPodcast={handleLoadPodcast}
              onRefresh={loadPodcastHistory}
            />
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings">
            <SettingsPanel
              settings={state.settings}
              onUpdateSettings={handleUpdateSettings}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
