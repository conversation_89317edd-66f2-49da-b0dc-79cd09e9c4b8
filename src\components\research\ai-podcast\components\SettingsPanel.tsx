import React from 'react';
import { GenerationSettings, PodcastStyle, PodcastLength } from '../types';
import { AI_MODELS, PODCAST_STYLES, PODCAST_LENGTHS, VOICE_OPTIONS } from '../constants';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { 
  Settings, 
  Mic, 
  Volume2, 
  Zap,
  Play,
  User,
  Users
} from 'lucide-react';

interface SettingsPanelProps {
  settings: GenerationSettings;
  onUpdateSettings: (newSettings: Partial<GenerationSettings>) => void;
}

export function SettingsPanel({ settings, onUpdateSettings }: SettingsPanelProps) {
  const handleModelChange = (model: string) => {
    onUpdateSettings({ model });
  };

  const handleStyleChange = (style: PodcastStyle) => {
    onUpdateSettings({ style });
  };

  const handleLengthChange = (length: PodcastLength) => {
    onUpdateSettings({ length });
  };

  const handleVoiceChange = (voiceType: 'hostVoice' | 'guestVoice', voiceId: string) => {
    onUpdateSettings({
      voiceSettings: {
        ...settings.voiceSettings,
        [voiceType]: voiceId,
      },
    });
  };

  const handleVoiceSettingChange = (setting: 'speed' | 'pitch', value: number) => {
    onUpdateSettings({
      voiceSettings: {
        ...settings.voiceSettings,
        [setting]: value,
      },
    });
  };

  const handleLanguageChange = (language: string) => {
    onUpdateSettings({ language });
  };

  const playVoicePreview = (voiceId: string) => {
    // In real implementation, play voice preview
    console.log('Playing voice preview for:', voiceId);
  };

  return (
    <div className="space-y-6">
      {/* AI Model Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            AI Model
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Label>Choose the AI model for content generation</Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {Object.entries(AI_MODELS).map(([key, model]) => (
                <button
                  key={model}
                  onClick={() => handleModelChange(model)}
                  className={`p-4 border rounded-lg text-left transition-colors ${
                    settings.model === model
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">{model}</div>
                  <div className="text-sm text-gray-600 mt-1">
                    {key === 'GEMINI_PRO' 
                      ? 'Best quality, slower generation'
                      : 'Faster generation, good quality'
                    }
                  </div>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Podcast Style */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mic className="h-5 w-5" />
            Podcast Style
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Label>Select the conversation style for your podcast</Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {PODCAST_STYLES.map((style) => (
                <button
                  key={style.id}
                  onClick={() => handleStyleChange(style.id)}
                  className={`p-4 border rounded-lg text-left transition-colors ${
                    settings.style === style.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg">{style.icon}</span>
                    <span className="font-medium">{style.name}</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {style.description}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Podcast Length */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Podcast Length
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Label>Choose the target length for your podcast</Label>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              {PODCAST_LENGTHS.map((length) => (
                <button
                  key={length.id}
                  onClick={() => handleLengthChange(length.id)}
                  className={`p-4 border rounded-lg text-center transition-colors ${
                    settings.length === length.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">{length.name}</div>
                  <div className="text-sm text-gray-600 mt-1">
                    {length.duration}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {length.wordCount}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Voice Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            Voice Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Host Voice */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Host Voice
            </Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {VOICE_OPTIONS.HOST_VOICES.map((voice) => (
                <div
                  key={voice.id}
                  className={`p-3 border rounded-lg transition-colors ${
                    settings.voiceSettings.hostVoice === voice.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => handleVoiceChange('hostVoice', voice.id)}
                      className="flex-1 text-left"
                    >
                      <div className="font-medium">{voice.name}</div>
                    </button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => playVoicePreview(voice.id)}
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Guest Voice */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Guest Voice
            </Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {VOICE_OPTIONS.GUEST_VOICES.map((voice) => (
                <div
                  key={voice.id}
                  className={`p-3 border rounded-lg transition-colors ${
                    settings.voiceSettings.guestVoice === voice.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => handleVoiceChange('guestVoice', voice.id)}
                      className="flex-1 text-left"
                    >
                      <div className="font-medium">{voice.name}</div>
                    </button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => playVoicePreview(voice.id)}
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Voice Controls */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label>Speech Speed</Label>
              <div className="px-3">
                <Slider
                  value={[settings.voiceSettings.speed]}
                  onValueChange={([value]) => handleVoiceSettingChange('speed', value)}
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0.5x</span>
                  <span>{settings.voiceSettings.speed}x</span>
                  <span>2.0x</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Voice Pitch</Label>
              <div className="px-3">
                <Slider
                  value={[settings.voiceSettings.pitch]}
                  onValueChange={([value]) => handleVoiceSettingChange('pitch', value)}
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0.5x</span>
                  <span>{settings.voiceSettings.pitch}x</span>
                  <span>2.0x</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Language Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Language Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Label>Output Language</Label>
            <select
              value={settings.language}
              onChange={(e) => handleLanguageChange(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="it">Italian</option>
              <option value="pt">Portuguese</option>
              <option value="zh">Chinese</option>
              <option value="ja">Japanese</option>
              <option value="ko">Korean</option>
            </select>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
