import React, { useState } from 'react';
import { GenerationSettings, PodcastStyle, PodcastLength, VoiceOption } from '../types';
import {
  CONTENT_GENERATION_MODELS,
  AUDIO_MODELS,
  PODCAST_STYLES,
  PODCAST_LENGTHS,
  VOICE_OPTIONS,
  AUDIO_MODEL_CONFIGS,
  CONTENT_MODEL_CONFIGS,
  AUDIO_QUALITY_SETTINGS,
  BACKGROUND_MUSIC_OPTIONS
} from '../constants';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import {
  Settings,
  Mic,
  Volume2,
  Zap,
  Play,
  User,
  Users,
  Crown,
  Clock,
  Star,
  Music,
  Headphones,
  Sparkles,
  Brain,
  Wand2,
  AudioWaveform,
  Sliders,
  Globe,
  DollarSign
} from 'lucide-react';

interface SettingsPanelProps {
  settings: GenerationSettings;
  onUpdateSettings: (newSettings: Partial<GenerationSettings>) => void;
}

export function SettingsPanel({ settings, onUpdateSettings }: SettingsPanelProps) {
  const [activeVoiceProvider, setActiveVoiceProvider] = useState<'elevenlabs' | 'google' | 'openai'>('elevenlabs');
  const [isPlayingPreview, setIsPlayingPreview] = useState<string | null>(null);

  const handleContentModelChange = (contentModel: string) => {
    onUpdateSettings({ contentModel });
  };

  const handleAudioModelChange = (audioModel: string) => {
    onUpdateSettings({ audioModel });
  };

  const handleStyleChange = (style: PodcastStyle) => {
    onUpdateSettings({ style });
  };

  const handleLengthChange = (length: PodcastLength) => {
    onUpdateSettings({ length });
  };

  const handleVoiceChange = (voiceType: 'hostVoice' | 'guestVoice', voiceId: string) => {
    const selectedVoice = getAllVoices().find(v => v.id === voiceId);
    onUpdateSettings({
      voiceSettings: {
        ...settings.voiceSettings,
        [voiceType]: voiceId,
        voiceProvider: selectedVoice?.provider,
      },
    });
  };

  const handleVoiceSettingChange = (setting: keyof typeof settings.voiceSettings, value: number | boolean) => {
    onUpdateSettings({
      voiceSettings: {
        ...settings.voiceSettings,
        [setting]: value,
      },
    });
  };

  const handleLanguageChange = (language: string) => {
    onUpdateSettings({ language });
  };

  const handleAudioQualityChange = (audioQuality: 'low' | 'standard' | 'high' | 'premium') => {
    onUpdateSettings({ audioQuality });
  };

  const handleBackgroundMusicChange = (enabled: boolean) => {
    onUpdateSettings({ backgroundMusic: enabled });
  };

  const handleMusicVolumeChange = (volume: number) => {
    onUpdateSettings({ musicVolume: volume });
  };

  const playVoicePreview = async (voiceId: string) => {
    setIsPlayingPreview(voiceId);
    // In real implementation, play voice preview
    console.log('Playing voice preview for:', voiceId);
    setTimeout(() => setIsPlayingPreview(null), 3000);
  };

  const getAllVoices = (): VoiceOption[] => {
    return [
      ...VOICE_OPTIONS.ELEVENLABS.HOST_VOICES,
      ...VOICE_OPTIONS.ELEVENLABS.GUEST_VOICES,
      ...VOICE_OPTIONS.GOOGLE.HOST_VOICES,
      ...VOICE_OPTIONS.GOOGLE.GUEST_VOICES,
      ...VOICE_OPTIONS.OPENAI.HOST_VOICES,
      ...VOICE_OPTIONS.OPENAI.GUEST_VOICES,
    ];
  };

  const getVoicesByProvider = (provider: string, type: 'HOST_VOICES' | 'GUEST_VOICES') => {
    switch (provider) {
      case 'elevenlabs':
        return VOICE_OPTIONS.ELEVENLABS[type];
      case 'google':
        return VOICE_OPTIONS.GOOGLE[type];
      case 'openai':
        return VOICE_OPTIONS.OPENAI[type];
      default:
        return [];
    }
  };

  const getModelBadgeColor = (pricing: string) => {
    switch (pricing) {
      case 'premium':
        return 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white';
      case 'standard':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getQualityBadgeColor = (quality: string) => {
    switch (quality) {
      case 'highest':
        return 'bg-gradient-to-r from-purple-500 to-pink-500 text-white';
      case 'high':
        return 'bg-green-100 text-green-800';
      case 'good':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-8">
      {/* Settings Tabs */}
      <Tabs defaultValue="models" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="models" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Models
          </TabsTrigger>
          <TabsTrigger value="voices" className="flex items-center gap-2">
            <Mic className="h-4 w-4" />
            Voices
          </TabsTrigger>
          <TabsTrigger value="audio" className="flex items-center gap-2">
            <AudioWaveform className="h-4 w-4" />
            Audio
          </TabsTrigger>
          <TabsTrigger value="style" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Style
          </TabsTrigger>
        </TabsList>

        {/* AI Models Tab */}
        <TabsContent value="models" className="space-y-6">
          {/* Content Generation Model */}
          <Card className="border-2 border-gradient-to-r from-blue-200 to-purple-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-600" />
                Content Generation Model
              </CardTitle>
              <p className="text-sm text-gray-600">Choose the AI model for creating podcast scripts and outlines</p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {Object.entries(CONTENT_MODEL_CONFIGS).map(([modelId, config]) => (
                  <button
                    key={modelId}
                    onClick={() => handleContentModelChange(modelId)}
                    className={`p-6 border-2 rounded-xl text-left transition-all duration-200 hover:shadow-lg ${
                      settings.contentModel === modelId
                        ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-purple-50 shadow-md'
                        : 'border-gray-200 hover:border-gray-300 bg-white'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="font-semibold text-lg">{config.name}</div>
                      <div className="flex gap-2">
                        <Badge className={getModelBadgeColor(config.pricing)}>
                          {config.pricing === 'premium' && <Crown className="h-3 w-3 mr-1" />}
                          {config.pricing}
                        </Badge>
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {config.speed}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{config.description}</p>
                    <div className="flex flex-wrap gap-1">
                      {config.features.map((feature) => (
                        <Badge key={feature} variant="secondary" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                    <div className="mt-3 text-xs text-gray-500">
                      Max tokens: {config.maxTokens.toLocaleString()}
                    </div>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Audio Generation Model */}
          <Card className="border-2 border-gradient-to-r from-green-200 to-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AudioWaveform className="h-5 w-5 text-green-600" />
                Audio Generation Model
              </CardTitle>
              <p className="text-sm text-gray-600">Select the text-to-speech engine for generating podcast audio</p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                {Object.entries(AUDIO_MODEL_CONFIGS).map(([modelId, config]) => (
                  <button
                    key={modelId}
                    onClick={() => handleAudioModelChange(modelId)}
                    className={`p-5 border-2 rounded-xl text-left transition-all duration-200 hover:shadow-lg ${
                      settings.audioModel === modelId
                        ? 'border-green-500 bg-gradient-to-br from-green-50 to-blue-50 shadow-md'
                        : 'border-gray-200 hover:border-gray-300 bg-white'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="font-semibold">{config.name}</div>
                      <div className="flex gap-1">
                        <Badge className={getModelBadgeColor(config.pricing)} size="sm">
                          {config.pricing === 'premium' && <Crown className="h-2 w-2 mr-1" />}
                          {config.pricing}
                        </Badge>
                      </div>
                    </div>
                    <div className="mb-2">
                      <Badge className={getQualityBadgeColor(config.quality)} size="sm">
                        <Star className="h-2 w-2 mr-1" />
                        {config.quality}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 mb-3">{config.description}</p>
                    <div className="flex flex-wrap gap-1 mb-2">
                      {config.features.slice(0, 3).map((feature) => (
                        <Badge key={feature} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                    <div className="text-xs text-gray-500">
                      <div>Max chars: {config.maxCharacters}</div>
                      <div>Languages: {config.supportedLanguages.length}</div>
                    </div>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Voices Tab */}
        <TabsContent value="voices" className="space-y-6">
          {/* Voice Provider Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Headphones className="h-5 w-5" />
                Voice Provider
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-3">
                {['elevenlabs', 'google', 'openai'].map((provider) => (
                  <button
                    key={provider}
                    onClick={() => setActiveVoiceProvider(provider as any)}
                    className={`p-4 border-2 rounded-lg text-center transition-all ${
                      activeVoiceProvider === provider
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium capitalize">{provider}</div>
                    <div className="text-xs text-gray-600 mt-1">
                      {provider === 'elevenlabs' && 'Premium quality'}
                      {provider === 'google' && 'Neural voices'}
                      {provider === 'openai' && 'Natural speech'}
                    </div>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Host Voice Selection */}
          <Card className="border-2 border-blue-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                Host Voice
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getVoicesByProvider(activeVoiceProvider, 'HOST_VOICES').map((voice: any) => (
                  <button
                    key={voice.id}
                    onClick={() => handleVoiceChange('hostVoice', voice.id)}
                    className={`p-4 border-2 rounded-lg text-left transition-all hover:shadow-md ${
                      settings.voiceSettings.hostVoice === voice.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="font-medium">{voice.name}</div>
                      <Badge variant="outline" className="text-xs">
                        {voice.gender}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600 mb-2">{voice.description}</div>
                    <div className="text-xs text-gray-500 mb-3">{voice.accent} accent</div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        playVoicePreview(voice.id);
                      }}
                      disabled={isPlayingPreview === voice.id}
                      className="w-full"
                    >
                      {isPlayingPreview === voice.id ? (
                        <Volume2 className="h-3 w-3 mr-2 animate-pulse" />
                      ) : (
                        <Play className="h-3 w-3 mr-2" />
                      )}
                      {isPlayingPreview === voice.id ? 'Playing...' : 'Preview'}
                    </Button>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Guest Voice Selection */}
          <Card className="border-2 border-green-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-green-600" />
                Guest Voice
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getVoicesByProvider(activeVoiceProvider, 'GUEST_VOICES').map((voice: any) => (
                  <button
                    key={voice.id}
                    onClick={() => handleVoiceChange('guestVoice', voice.id)}
                    className={`p-4 border-2 rounded-lg text-left transition-all hover:shadow-md ${
                      settings.voiceSettings.guestVoice === voice.id
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="font-medium">{voice.name}</div>
                      <Badge variant="outline" className="text-xs">
                        {voice.gender}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600 mb-2">{voice.description}</div>
                    <div className="text-xs text-gray-500 mb-3">{voice.accent} accent</div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        playVoicePreview(voice.id);
                      }}
                      disabled={isPlayingPreview === voice.id}
                      className="w-full"
                    >
                      {isPlayingPreview === voice.id ? (
                        <Volume2 className="h-3 w-3 mr-2 animate-pulse" />
                      ) : (
                        <Play className="h-3 w-3 mr-2" />
                      )}
                      {isPlayingPreview === voice.id ? 'Playing...' : 'Preview'}
                    </Button>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Voice Controls */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sliders className="h-5 w-5" />
                Voice Controls
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Controls */}
                <div className="space-y-4">
                  <div>
                    <Label className="flex items-center justify-between">
                      Speech Speed
                      <span className="text-sm text-gray-500">{settings.voiceSettings.speed}x</span>
                    </Label>
                    <Slider
                      value={[settings.voiceSettings.speed]}
                      onValueChange={([value]) => handleVoiceSettingChange('speed', value)}
                      min={0.5}
                      max={2.0}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label className="flex items-center justify-between">
                      Voice Pitch
                      <span className="text-sm text-gray-500">{settings.voiceSettings.pitch}x</span>
                    </Label>
                    <Slider
                      value={[settings.voiceSettings.pitch]}
                      onValueChange={([value]) => handleVoiceSettingChange('pitch', value)}
                      min={0.5}
                      max={2.0}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                </div>

                {/* Advanced Controls (ElevenLabs) */}
                {activeVoiceProvider === 'elevenlabs' && (
                  <div className="space-y-4">
                    <div>
                      <Label className="flex items-center justify-between">
                        Stability
                        <span className="text-sm text-gray-500">{settings.voiceSettings.stability || 0.75}</span>
                      </Label>
                      <Slider
                        value={[settings.voiceSettings.stability || 0.75]}
                        onValueChange={([value]) => handleVoiceSettingChange('stability', value)}
                        min={0}
                        max={1}
                        step={0.05}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <Label className="flex items-center justify-between">
                        Clarity
                        <span className="text-sm text-gray-500">{settings.voiceSettings.clarity || 0.75}</span>
                      </Label>
                      <Slider
                        value={[settings.voiceSettings.clarity || 0.75]}
                        onValueChange={([value]) => handleVoiceSettingChange('clarity', value)}
                        min={0}
                        max={1}
                        step={0.05}
                        className="mt-2"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label>Speaker Boost</Label>
                      <Switch
                        checked={settings.voiceSettings.speakerBoost || false}
                        onCheckedChange={(checked) => handleVoiceSettingChange('speakerBoost', checked)}
                      />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Audio Tab */}
        <TabsContent value="audio" className="space-y-6">
          {/* Audio Quality */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AudioWaveform className="h-5 w-5" />
                Audio Quality
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(AUDIO_QUALITY_SETTINGS).map(([qualityId, quality]) => (
                  <button
                    key={qualityId}
                    onClick={() => handleAudioQualityChange(qualityId as any)}
                    className={`p-4 border-2 rounded-lg text-center transition-all ${
                      settings.audioQuality === qualityId
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium">{quality.name}</div>
                    <div className="text-xs text-gray-600 mt-1">{quality.description}</div>
                    <div className="text-xs text-gray-500 mt-2">
                      <div>{quality.sampleRate}Hz</div>
                      <div>{quality.bitRate}kbps</div>
                      <div>{quality.format.toUpperCase()}</div>
                    </div>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Background Music */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Music className="h-5 w-5" />
                Background Music
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Enable Background Music</Label>
                <Switch
                  checked={settings.backgroundMusic}
                  onCheckedChange={handleBackgroundMusicChange}
                />
              </div>

              {settings.backgroundMusic && (
                <>
                  <div>
                    <Label className="flex items-center justify-between">
                      Music Volume
                      <span className="text-sm text-gray-500">{Math.round(settings.musicVolume * 100)}%</span>
                    </Label>
                    <Slider
                      value={[settings.musicVolume]}
                      onValueChange={([value]) => handleMusicVolumeChange(value)}
                      min={0}
                      max={0.5}
                      step={0.05}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label>Music Style</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                      {BACKGROUND_MUSIC_OPTIONS.map((music) => (
                        <button
                          key={music.id}
                          onClick={() => onUpdateSettings({ backgroundMusicType: music.id })}
                          className={`p-3 border rounded-lg text-left transition-colors ${
                            settings.backgroundMusicType === music.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="font-medium text-sm">{music.name}</div>
                          <div className="text-xs text-gray-600">{music.description}</div>
                        </button>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Language Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Language Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label>Output Language</Label>
                <select
                  value={settings.language}
                  onChange={(e) => handleLanguageChange(e.target.value)}
                  className="w-full mt-2 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="it">Italian</option>
                  <option value="pt">Portuguese</option>
                  <option value="zh">Chinese</option>
                  <option value="ja">Japanese</option>
                  <option value="ko">Korean</option>
                  <option value="hi">Hindi</option>
                  <option value="ar">Arabic</option>
                </select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Style Tab */}
        <TabsContent value="style" className="space-y-6">
          {/* Podcast Style */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="h-5 w-5" />
                Podcast Style
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {PODCAST_STYLES.map((style) => (
                  <button
                    key={style.id}
                    onClick={() => handleStyleChange(style.id)}
                    className={`p-6 border-2 rounded-xl text-left transition-all hover:shadow-lg ${
                      settings.style === style.id
                        ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-purple-50 shadow-md'
                        : 'border-gray-200 hover:border-gray-300 bg-white'
                    }`}
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <span className="text-2xl">{style.icon}</span>
                      <span className="font-semibold text-lg">{style.name}</span>
                    </div>
                    <p className="text-sm text-gray-600">{style.description}</p>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Podcast Length */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Podcast Length
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {PODCAST_LENGTHS.map((length) => (
                  <button
                    key={length.id}
                    onClick={() => handleLengthChange(length.id)}
                    className={`p-6 border-2 rounded-xl text-center transition-all hover:shadow-lg ${
                      settings.length === length.id
                        ? 'border-green-500 bg-gradient-to-br from-green-50 to-blue-50 shadow-md'
                        : 'border-gray-200 hover:border-gray-300 bg-white'
                    }`}
                  >
                    <div className="font-semibold text-xl mb-2">{length.name}</div>
                    <div className="text-lg text-gray-700 mb-2">{length.duration}</div>
                    <div className="text-sm text-gray-600">{length.wordCount}</div>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Cost Estimation */}
          <Card className="border-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-yellow-600" />
                Estimated Cost
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div className="p-3 bg-white rounded-lg border">
                  <div className="text-sm text-gray-600">Content Generation</div>
                  <div className="font-semibold text-lg">$0.05</div>
                </div>
                <div className="p-3 bg-white rounded-lg border">
                  <div className="text-sm text-gray-600">Audio Generation</div>
                  <div className="font-semibold text-lg">$0.15</div>
                </div>
                <div className="p-3 bg-white rounded-lg border">
                  <div className="text-sm text-gray-600">Processing</div>
                  <div className="font-semibold text-lg">$0.02</div>
                </div>
                <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg">
                  <div className="text-sm opacity-90">Total</div>
                  <div className="font-bold text-xl">$0.22</div>
                </div>
              </div>
              <p className="text-xs text-gray-600 mt-3 text-center">
                Estimated cost based on current settings. Actual cost may vary.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

      {/* Podcast Style */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mic className="h-5 w-5" />
            Podcast Style
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Label>Select the conversation style for your podcast</Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {PODCAST_STYLES.map((style) => (
                <button
                  key={style.id}
                  onClick={() => handleStyleChange(style.id)}
                  className={`p-4 border rounded-lg text-left transition-colors ${
                    settings.style === style.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg">{style.icon}</span>
                    <span className="font-medium">{style.name}</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {style.description}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Podcast Length */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Podcast Length
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Label>Choose the target length for your podcast</Label>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              {PODCAST_LENGTHS.map((length) => (
                <button
                  key={length.id}
                  onClick={() => handleLengthChange(length.id)}
                  className={`p-4 border rounded-lg text-center transition-colors ${
                    settings.length === length.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">{length.name}</div>
                  <div className="text-sm text-gray-600 mt-1">
                    {length.duration}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {length.wordCount}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Voice Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            Voice Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Host Voice */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Host Voice
            </Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {VOICE_OPTIONS.HOST_VOICES.map((voice) => (
                <div
                  key={voice.id}
                  className={`p-3 border rounded-lg transition-colors ${
                    settings.voiceSettings.hostVoice === voice.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => handleVoiceChange('hostVoice', voice.id)}
                      className="flex-1 text-left"
                    >
                      <div className="font-medium">{voice.name}</div>
                    </button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => playVoicePreview(voice.id)}
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Guest Voice */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Guest Voice
            </Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {VOICE_OPTIONS.GUEST_VOICES.map((voice) => (
                <div
                  key={voice.id}
                  className={`p-3 border rounded-lg transition-colors ${
                    settings.voiceSettings.guestVoice === voice.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => handleVoiceChange('guestVoice', voice.id)}
                      className="flex-1 text-left"
                    >
                      <div className="font-medium">{voice.name}</div>
                    </button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => playVoicePreview(voice.id)}
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Voice Controls */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label>Speech Speed</Label>
              <div className="px-3">
                <Slider
                  value={[settings.voiceSettings.speed]}
                  onValueChange={([value]) => handleVoiceSettingChange('speed', value)}
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0.5x</span>
                  <span>{settings.voiceSettings.speed}x</span>
                  <span>2.0x</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Voice Pitch</Label>
              <div className="px-3">
                <Slider
                  value={[settings.voiceSettings.pitch]}
                  onValueChange={([value]) => handleVoiceSettingChange('pitch', value)}
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0.5x</span>
                  <span>{settings.voiceSettings.pitch}x</span>
                  <span>2.0x</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Language Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Language Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Label>Output Language</Label>
            <select
              value={settings.language}
              onChange={(e) => handleLanguageChange(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="it">Italian</option>
              <option value="pt">Portuguese</option>
              <option value="zh">Chinese</option>
              <option value="ja">Japanese</option>
              <option value="ko">Korean</option>
            </select>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
