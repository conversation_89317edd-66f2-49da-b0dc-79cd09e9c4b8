import { LucideIcon } from "lucide-react";

// Podcast generation types
export interface PodcastMetadata {
  title: string;
  description: string;
  duration?: number;
  language: string;
  tags: string[];
}

export interface ContentSource {
  id: string;
  type: 'pdf' | 'text' | 'url' | 'document';
  name: string;
  content: string;
  size?: number;
  uploadedAt: Date;
  extractedText?: string;
}

export interface PodcastScript {
  id: string;
  segments: PodcastSegment[];
  totalDuration?: number;
  wordCount: number;
}

export interface PodcastSegment {
  id: string;
  speaker: 'host' | 'guest';
  text: string;
  duration?: number;
  order: number;
  timestamp?: number;
}

export interface GeneratedPodcast {
  id: string;
  metadata: PodcastMetadata;
  script: PodcastScript;
  audioUrl?: string;
  status: PodcastStatus;
  createdAt: Date;
  updatedAt: Date;
  sources: ContentSource[];
  generationSettings: GenerationSettings;
}

export interface GenerationSettings {
  contentModel: string;
  audioModel: string;
  voiceSettings: VoiceSettings;
  style: PodcastStyle;
  length: PodcastLength;
  language: string;
  audioQuality: 'low' | 'standard' | 'high' | 'premium';
  backgroundMusic: boolean;
  musicVolume: number;
  backgroundMusicType?: string;
}

export interface VoiceSettings {
  hostVoice: string;
  guestVoice: string;
  speed: number;
  pitch: number;
  stability?: number;
  clarity?: number;
  style?: number;
  speakerBoost?: boolean;
  emotionalRange?: number;
  voiceProvider?: 'elevenlabs' | 'google' | 'openai' | 'azure';
}

export enum PodcastStatus {
  DRAFT = 'draft',
  GENERATING_OUTLINE = 'generating_outline',
  GENERATING_SCRIPT = 'generating_script',
  GENERATING_AUDIO = 'generating_audio',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export enum PodcastStyle {
  CONVERSATIONAL = 'conversational',
  INTERVIEW = 'interview',
  EDUCATIONAL = 'educational',
  DEBATE = 'debate',
  STORYTELLING = 'storytelling'
}

export enum PodcastLength {
  SHORT = 'short', // 3-5 minutes
  MEDIUM = 'medium', // 8-12 minutes
  LONG = 'long' // 15-20 minutes
}

export enum ContentType {
  PDF = 'pdf',
  TEXT = 'text',
  URL = 'url',
  DOCUMENT = 'document'
}

// UI Component types
export interface PodcastGeneratorState {
  sources: ContentSource[];
  currentPodcast?: GeneratedPodcast;
  isGenerating: boolean;
  generationStep: GenerationStep;
  error?: string;
  settings: GenerationSettings;
}

export enum GenerationStep {
  UPLOAD = 'upload',
  CONFIGURE = 'configure',
  OUTLINE = 'outline',
  SCRIPT = 'script',
  AUDIO = 'audio',
  COMPLETE = 'complete'
}

export interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

// Voice option interface
export interface VoiceOption {
  id: string;
  name: string;
  gender: 'male' | 'female' | 'neutral';
  accent: string;
  description: string;
  preview?: string;
  provider: 'elevenlabs' | 'google' | 'openai' | 'azure';
  features?: string[];
  pricing?: 'free' | 'standard' | 'premium';
}

// Audio model configuration
export interface AudioModelConfig {
  name: string;
  provider: string;
  description: string;
  maxCharacters: number;
  supportedLanguages: string[];
  features: string[];
  pricing: 'free' | 'standard' | 'premium';
  quality: 'low' | 'good' | 'high' | 'highest';
}

// Content model configuration
export interface ContentModelConfig {
  name: string;
  provider: string;
  description: string;
  maxTokens: number;
  features: string[];
  pricing: 'free' | 'standard' | 'premium';
  speed: 'slow' | 'medium' | 'fast';
}

// Audio generation request
export interface AudioGenerationRequest {
  text: string;
  voiceId: string;
  model: string;
  settings: {
    speed?: number;
    pitch?: number;
    stability?: number;
    clarity?: number;
    style?: number;
    speakerBoost?: boolean;
  };
  format?: 'mp3' | 'wav' | 'ogg';
  quality?: 'low' | 'standard' | 'high' | 'premium';
}

// API Response types
export interface PodcastGenerationResponse {
  success: boolean;
  data?: GeneratedPodcast;
  error?: string;
  progress?: number;
  estimatedTime?: number;
  tokensUsed?: number;
  cost?: number;
}

export interface OutlineGenerationResponse {
  title: string;
  outline: string;
  keyPoints: string[];
  estimatedDuration: number;
}

export interface ScriptGenerationResponse {
  script: PodcastSegment[];
  metadata: {
    wordCount: number;
    estimatedDuration: number;
    speakerBalance: {
      host: number;
      guest: number;
    };
  };
}

// Supabase database types
export interface PodcastRecord {
  id: string;
  user_id: string;
  title: string;
  description: string;
  script_data: any;
  audio_url?: string;
  status: string;
  metadata: any;
  sources: any[];
  created_at: string;
  updated_at: string;
}

export interface ContentSourceRecord {
  id: string;
  podcast_id: string;
  type: string;
  name: string;
  content: string;
  file_url?: string;
  extracted_text?: string;
  created_at: string;
}
