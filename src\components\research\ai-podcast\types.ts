import { LucideIcon } from "lucide-react";

// Podcast generation types
export interface PodcastMetadata {
  title: string;
  description: string;
  duration?: number;
  language: string;
  tags: string[];
}

export interface ContentSource {
  id: string;
  type: 'pdf' | 'text' | 'url' | 'document';
  name: string;
  content: string;
  size?: number;
  uploadedAt: Date;
  extractedText?: string;
}

export interface PodcastScript {
  id: string;
  segments: PodcastSegment[];
  totalDuration?: number;
  wordCount: number;
}

export interface PodcastSegment {
  id: string;
  speaker: 'host' | 'guest';
  text: string;
  duration?: number;
  order: number;
  timestamp?: number;
}

export interface GeneratedPodcast {
  id: string;
  metadata: PodcastMetadata;
  script: PodcastScript;
  audioUrl?: string;
  status: PodcastStatus;
  createdAt: Date;
  updatedAt: Date;
  sources: ContentSource[];
  generationSettings: GenerationSettings;
}

export interface GenerationSettings {
  model: string;
  voiceSettings: VoiceSettings;
  style: PodcastStyle;
  length: PodcastLength;
  language: string;
}

export interface VoiceSettings {
  hostVoice: string;
  guestVoice: string;
  speed: number;
  pitch: number;
}

export enum PodcastStatus {
  DRAFT = 'draft',
  GENERATING_OUTLINE = 'generating_outline',
  GENERATING_SCRIPT = 'generating_script',
  GENERATING_AUDIO = 'generating_audio',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export enum PodcastStyle {
  CONVERSATIONAL = 'conversational',
  INTERVIEW = 'interview',
  EDUCATIONAL = 'educational',
  DEBATE = 'debate',
  STORYTELLING = 'storytelling'
}

export enum PodcastLength {
  SHORT = 'short', // 3-5 minutes
  MEDIUM = 'medium', // 8-12 minutes
  LONG = 'long' // 15-20 minutes
}

export enum ContentType {
  PDF = 'pdf',
  TEXT = 'text',
  URL = 'url',
  DOCUMENT = 'document'
}

// UI Component types
export interface PodcastGeneratorState {
  sources: ContentSource[];
  currentPodcast?: GeneratedPodcast;
  isGenerating: boolean;
  generationStep: GenerationStep;
  error?: string;
  settings: GenerationSettings;
}

export enum GenerationStep {
  UPLOAD = 'upload',
  CONFIGURE = 'configure',
  OUTLINE = 'outline',
  SCRIPT = 'script',
  AUDIO = 'audio',
  COMPLETE = 'complete'
}

export interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

// API Response types
export interface PodcastGenerationResponse {
  success: boolean;
  data?: GeneratedPodcast;
  error?: string;
  progress?: number;
}

export interface OutlineGenerationResponse {
  title: string;
  outline: string;
  keyPoints: string[];
  estimatedDuration: number;
}

export interface ScriptGenerationResponse {
  script: PodcastSegment[];
  metadata: {
    wordCount: number;
    estimatedDuration: number;
    speakerBalance: {
      host: number;
      guest: number;
    };
  };
}

// Supabase database types
export interface PodcastRecord {
  id: string;
  user_id: string;
  title: string;
  description: string;
  script_data: any;
  audio_url?: string;
  status: string;
  metadata: any;
  sources: any[];
  created_at: string;
  updated_at: string;
}

export interface ContentSourceRecord {
  id: string;
  podcast_id: string;
  type: string;
  name: string;
  content: string;
  file_url?: string;
  extracted_text?: string;
  created_at: string;
}
