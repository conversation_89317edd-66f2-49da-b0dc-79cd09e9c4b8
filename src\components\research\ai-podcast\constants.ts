import { GenerationSettings, PodcastStyle, PodcastLength } from './types';

// AI Models for content generation
export const CONTENT_GENERATION_MODELS = {
  GEMINI_PRO: 'gemini-2.5-pro',
  GEMINI_FLASH: 'gemini-2.5-flash',
  GPT_4: 'gpt-4',
  GPT_4_TURBO: 'gpt-4-turbo',
  CLAUDE_SONNET: 'claude-3-sonnet',
  CLAUDE_HAIKU: 'claude-3-haiku',
} as const;

// Audio Generation Models
export const AUDIO_MODELS = {
  // Google Cloud Text-to-Speech
  GOOGLE_STANDARD: 'google-standard',
  GOOGLE_WAVENET: 'google-wavenet',
  GOOGLE_NEURAL2: 'google-neural2',
  GOOGLE_STUDIO: 'google-studio',

  // ElevenLabs
  ELEVENLABS_MULTILINGUAL_V2: 'eleven-multilingual-v2',
  ELEVENLABS_TURBO_V2: 'eleven-turbo-v2',
  ELEVENLABS_MULTILINGUAL_V1: 'eleven-multilingual-v1',
  ELEVENLABS_MONOLINGUAL_V1: 'eleven-monolingual-v1',

  // OpenAI
  OPENAI_TTS_1: 'tts-1',
  OPENAI_TTS_1_HD: 'tts-1-hd',

  // Azure Cognitive Services
  AZURE_NEURAL: 'azure-neural',
  AZURE_STANDARD: 'azure-standard',
} as const;

// Default generation settings
export const DEFAULT_GENERATION_SETTINGS: GenerationSettings = {
  contentModel: CONTENT_GENERATION_MODELS.GEMINI_PRO,
  audioModel: AUDIO_MODELS.ELEVENLABS_MULTILINGUAL_V2,
  voiceSettings: {
    hostVoice: 'rachel-elevenlabs',
    guestVoice: 'adam-elevenlabs',
    speed: 1.0,
    pitch: 1.0,
    stability: 0.75,
    clarity: 0.75,
    style: 0.0,
    speakerBoost: true,
  },
  style: PodcastStyle.CONVERSATIONAL,
  length: PodcastLength.MEDIUM,
  language: 'en',
  audioQuality: 'high',
  backgroundMusic: false,
  musicVolume: 0.1,
};

// Supported file types for upload
export const SUPPORTED_FILE_TYPES = {
  PDF: '.pdf',
  DOC: '.doc,.docx',
  TXT: '.txt',
  RTF: '.rtf',
} as const;

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_TEXT_LENGTH = 50000; // 50k characters
export const MAX_SOURCES = 5; // Maximum number of content sources

// Podcast styles with descriptions
export const PODCAST_STYLES = [
  {
    id: PodcastStyle.CONVERSATIONAL,
    name: 'Conversational',
    description: 'Natural, friendly discussion between two people',
    icon: '💬',
  },
  {
    id: PodcastStyle.INTERVIEW,
    name: 'Interview',
    description: 'Structured Q&A format with host asking questions',
    icon: '🎤',
  },
  {
    id: PodcastStyle.EDUCATIONAL,
    name: 'Educational',
    description: 'Informative content with clear explanations',
    icon: '📚',
  },
  {
    id: PodcastStyle.DEBATE,
    name: 'Debate',
    description: 'Discussion of different viewpoints and perspectives',
    icon: '⚖️',
  },
  {
    id: PodcastStyle.STORYTELLING,
    name: 'Storytelling',
    description: 'Narrative format with engaging storytelling elements',
    icon: '📖',
  },
] as const;

// Podcast lengths with descriptions
export const PODCAST_LENGTHS = [
  {
    id: PodcastLength.SHORT,
    name: 'Short',
    description: '3-5 minutes',
    duration: '3-5 min',
    wordCount: '400-600 words',
  },
  {
    id: PodcastLength.MEDIUM,
    name: 'Medium',
    description: '8-12 minutes',
    duration: '8-12 min',
    wordCount: '1000-1500 words',
  },
  {
    id: PodcastLength.LONG,
    name: 'Long',
    description: '15-20 minutes',
    duration: '15-20 min',
    wordCount: '2000-3000 words',
  },
] as const;

// Comprehensive Voice Options
export const VOICE_OPTIONS = {
  // ElevenLabs Voices
  ELEVENLABS: {
    HOST_VOICES: [
      { id: 'rachel-elevenlabs', name: 'Rachel', gender: 'female', accent: 'American', description: 'Calm, professional narrator', preview: '/audio/rachel-preview.mp3', provider: 'elevenlabs' },
      { id: 'drew-elevenlabs', name: 'Drew', gender: 'male', accent: 'American', description: 'Well-rounded, middle-aged', preview: '/audio/drew-preview.mp3', provider: 'elevenlabs' },
      { id: 'clyde-elevenlabs', name: 'Clyde', gender: 'male', accent: 'American', description: 'War veteran, storyteller', preview: '/audio/clyde-preview.mp3', provider: 'elevenlabs' },
      { id: 'paul-elevenlabs', name: 'Paul', gender: 'male', accent: 'American', description: 'Authoritative, news anchor', preview: '/audio/paul-preview.mp3', provider: 'elevenlabs' },
      { id: 'domi-elevenlabs', name: 'Domi', gender: 'female', accent: 'American', description: 'Strong, confident', preview: '/audio/domi-preview.mp3', provider: 'elevenlabs' },
    ],
    GUEST_VOICES: [
      { id: 'adam-elevenlabs', name: 'Adam', gender: 'male', accent: 'American', description: 'Deep, engaging', preview: '/audio/adam-preview.mp3', provider: 'elevenlabs' },
      { id: 'elli-elevenlabs', name: 'Elli', gender: 'female', accent: 'American', description: 'Emotional, young adult', preview: '/audio/elli-preview.mp3', provider: 'elevenlabs' },
      { id: 'josh-elevenlabs', name: 'Josh', gender: 'male', accent: 'American', description: 'Casual, conversational', preview: '/audio/josh-preview.mp3', provider: 'elevenlabs' },
      { id: 'arnold-elevenlabs', name: 'Arnold', gender: 'male', accent: 'American', description: 'Crisp, authoritative', preview: '/audio/arnold-preview.mp3', provider: 'elevenlabs' },
      { id: 'charlotte-elevenlabs', name: 'Charlotte', gender: 'female', accent: 'British', description: 'Seductive, confident', preview: '/audio/charlotte-preview.mp3', provider: 'elevenlabs' },
    ],
  },

  // Google Cloud TTS Voices
  GOOGLE: {
    HOST_VOICES: [
      { id: 'en-US-Neural2-A', name: 'Neural2 Male A', gender: 'male', accent: 'American', description: 'Professional, clear', provider: 'google' },
      { id: 'en-US-Neural2-C', name: 'Neural2 Female C', gender: 'female', accent: 'American', description: 'Warm, engaging', provider: 'google' },
      { id: 'en-US-Neural2-D', name: 'Neural2 Male D', gender: 'male', accent: 'American', description: 'Authoritative, deep', provider: 'google' },
      { id: 'en-US-Neural2-F', name: 'Neural2 Female F', gender: 'female', accent: 'American', description: 'Professional, crisp', provider: 'google' },
      { id: 'en-GB-Neural2-A', name: 'Neural2 Male A (UK)', gender: 'male', accent: 'British', description: 'Sophisticated, clear', provider: 'google' },
    ],
    GUEST_VOICES: [
      { id: 'en-US-Neural2-B', name: 'Neural2 Male B', gender: 'male', accent: 'American', description: 'Conversational, friendly', provider: 'google' },
      { id: 'en-US-Neural2-E', name: 'Neural2 Female E', gender: 'female', accent: 'American', description: 'Expressive, dynamic', provider: 'google' },
      { id: 'en-US-Neural2-G', name: 'Neural2 Female G', gender: 'female', accent: 'American', description: 'Young, energetic', provider: 'google' },
      { id: 'en-US-Neural2-H', name: 'Neural2 Female H', gender: 'female', accent: 'American', description: 'Mature, wise', provider: 'google' },
      { id: 'en-GB-Neural2-B', name: 'Neural2 Female B (UK)', gender: 'female', accent: 'British', description: 'Elegant, articulate', provider: 'google' },
    ],
  },

  // OpenAI TTS Voices
  OPENAI: {
    HOST_VOICES: [
      { id: 'alloy', name: 'Alloy', gender: 'neutral', accent: 'American', description: 'Balanced, professional', provider: 'openai' },
      { id: 'echo', name: 'Echo', gender: 'male', accent: 'American', description: 'Authoritative, clear', provider: 'openai' },
      { id: 'nova', name: 'Nova', gender: 'female', accent: 'American', description: 'Energetic, engaging', provider: 'openai' },
    ],
    GUEST_VOICES: [
      { id: 'fable', name: 'Fable', gender: 'male', accent: 'British', description: 'Storytelling, warm', provider: 'openai' },
      { id: 'onyx', name: 'Onyx', gender: 'male', accent: 'American', description: 'Deep, resonant', provider: 'openai' },
      { id: 'shimmer', name: 'Shimmer', gender: 'female', accent: 'American', description: 'Soft, conversational', provider: 'openai' },
    ],
  },
} as const;

// Audio Model Configurations
export const AUDIO_MODEL_CONFIGS = {
  [AUDIO_MODELS.ELEVENLABS_MULTILINGUAL_V2]: {
    name: 'ElevenLabs Multilingual v2',
    provider: 'elevenlabs',
    description: 'High-quality, multilingual, most natural sounding',
    maxCharacters: 5000,
    supportedLanguages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'pl', 'hi', 'ar', 'zh', 'ja', 'ko'],
    features: ['emotion', 'stability', 'clarity', 'style'],
    pricing: 'premium',
    quality: 'highest',
  },
  [AUDIO_MODELS.ELEVENLABS_TURBO_V2]: {
    name: 'ElevenLabs Turbo v2',
    provider: 'elevenlabs',
    description: 'Fast generation, good quality, cost-effective',
    maxCharacters: 5000,
    supportedLanguages: ['en'],
    features: ['stability', 'clarity'],
    pricing: 'standard',
    quality: 'high',
  },
  [AUDIO_MODELS.GOOGLE_NEURAL2]: {
    name: 'Google Neural2',
    provider: 'google',
    description: 'Advanced neural voices, very natural',
    maxCharacters: 5000,
    supportedLanguages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ja', 'ko', 'zh'],
    features: ['pitch', 'speed', 'volume'],
    pricing: 'standard',
    quality: 'high',
  },
  [AUDIO_MODELS.GOOGLE_WAVENET]: {
    name: 'Google WaveNet',
    provider: 'google',
    description: 'High-quality neural network voices',
    maxCharacters: 5000,
    supportedLanguages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ja', 'ko'],
    features: ['pitch', 'speed'],
    pricing: 'standard',
    quality: 'high',
  },
  [AUDIO_MODELS.OPENAI_TTS_1_HD]: {
    name: 'OpenAI TTS-1-HD',
    provider: 'openai',
    description: 'High-definition text-to-speech',
    maxCharacters: 4096,
    supportedLanguages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'zh', 'ja', 'ko'],
    features: ['speed'],
    pricing: 'premium',
    quality: 'highest',
  },
  [AUDIO_MODELS.OPENAI_TTS_1]: {
    name: 'OpenAI TTS-1',
    provider: 'openai',
    description: 'Standard quality, faster generation',
    maxCharacters: 4096,
    supportedLanguages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'zh', 'ja', 'ko'],
    features: ['speed'],
    pricing: 'standard',
    quality: 'good',
  },
} as const;

// Audio Quality Settings
export const AUDIO_QUALITY_SETTINGS = {
  low: {
    name: 'Low Quality',
    description: 'Faster generation, smaller files',
    sampleRate: 22050,
    bitRate: 64,
    format: 'mp3',
  },
  standard: {
    name: 'Standard Quality',
    description: 'Balanced quality and speed',
    sampleRate: 44100,
    bitRate: 128,
    format: 'mp3',
  },
  high: {
    name: 'High Quality',
    description: 'Better audio quality',
    sampleRate: 44100,
    bitRate: 192,
    format: 'mp3',
  },
  premium: {
    name: 'Premium Quality',
    description: 'Highest quality, larger files',
    sampleRate: 48000,
    bitRate: 320,
    format: 'wav',
  },
} as const;

// Background Music Options
export const BACKGROUND_MUSIC_OPTIONS = [
  { id: 'none', name: 'No Music', description: 'Pure voice only' },
  { id: 'ambient-corporate', name: 'Corporate Ambient', description: 'Professional, subtle background' },
  { id: 'soft-piano', name: 'Soft Piano', description: 'Gentle piano melodies' },
  { id: 'nature-sounds', name: 'Nature Sounds', description: 'Subtle nature ambience' },
  { id: 'minimal-electronic', name: 'Minimal Electronic', description: 'Modern, tech-focused' },
  { id: 'acoustic-guitar', name: 'Acoustic Guitar', description: 'Warm, conversational' },
  { id: 'orchestral-light', name: 'Light Orchestral', description: 'Sophisticated, educational' },
] as const;

// Content Generation Model Configurations
export const CONTENT_MODEL_CONFIGS = {
  [CONTENT_GENERATION_MODELS.GEMINI_PRO]: {
    name: 'Google Gemini 2.5 Pro',
    provider: 'google',
    description: 'Most capable model, best for complex content',
    maxTokens: 1000000,
    features: ['reasoning', 'creativity', 'analysis'],
    pricing: 'premium',
    speed: 'slow',
  },
  [CONTENT_GENERATION_MODELS.GEMINI_FLASH]: {
    name: 'Google Gemini 2.5 Flash',
    provider: 'google',
    description: 'Fast and efficient, good quality',
    maxTokens: 1000000,
    features: ['speed', 'efficiency'],
    pricing: 'standard',
    speed: 'fast',
  },
  [CONTENT_GENERATION_MODELS.GPT_4]: {
    name: 'OpenAI GPT-4',
    provider: 'openai',
    description: 'Excellent reasoning and creativity',
    maxTokens: 128000,
    features: ['reasoning', 'creativity', 'accuracy'],
    pricing: 'premium',
    speed: 'medium',
  },
  [CONTENT_GENERATION_MODELS.CLAUDE_SONNET]: {
    name: 'Anthropic Claude 3 Sonnet',
    provider: 'anthropic',
    description: 'Balanced performance and speed',
    maxTokens: 200000,
    features: ['analysis', 'writing', 'reasoning'],
    pricing: 'standard',
    speed: 'medium',
  },
} as const;

// Generation prompts
export const GENERATION_PROMPTS = {
  OUTLINE: `
    Analyze the provided content and create a detailed podcast outline. 
    
    Requirements:
    - Create an engaging title that captures the main topic
    - Develop a structured outline with clear sections
    - Identify 3-5 key discussion points
    - Estimate total duration based on content depth
    - Ensure the outline flows naturally for a {style} style podcast
    - Target length: {length}
    
    Return the response in JSON format:
    {
      "title": "Engaging podcast title",
      "outline": "Detailed markdown outline",
      "keyPoints": ["point1", "point2", "point3"],
      "estimatedDuration": 600
    }
  `,
  
  SCRIPT: `
    Convert the provided outline and content into a natural podcast script for two speakers.
    
    Requirements:
    - Create engaging dialogue between a host and guest
    - Use conversational, natural language (avoid formal academic tone)
    - Include smooth transitions between topics
    - Maintain {style} style throughout
    - Target approximately {wordCount} words for {duration} duration
    - Host should guide the conversation and ask questions
    - Guest should provide detailed insights and explanations
    - Include natural speech patterns (pauses, emphasis, etc.)
    
    Return the response in JSON format:
    {
      "script": [
        {"speaker": "host", "text": "Welcome to today's discussion about...", "order": 1},
        {"speaker": "guest", "text": "Thanks for having me. This topic is fascinating because...", "order": 2}
      ],
      "metadata": {
        "wordCount": 1200,
        "estimatedDuration": 720,
        "speakerBalance": {"host": 40, "guest": 60}
      }
    }
  `,
} as const;

// Error messages
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: `File size exceeds ${MAX_FILE_SIZE / 1024 / 1024}MB limit`,
  UNSUPPORTED_FILE_TYPE: 'Unsupported file type. Please upload PDF, DOC, or TXT files.',
  TEXT_TOO_LONG: `Text content exceeds ${MAX_TEXT_LENGTH} character limit`,
  TOO_MANY_SOURCES: `Maximum ${MAX_SOURCES} content sources allowed`,
  GENERATION_FAILED: 'Failed to generate podcast. Please try again.',
  UPLOAD_FAILED: 'Failed to upload file. Please try again.',
  INVALID_URL: 'Please enter a valid URL',
  NO_CONTENT: 'Please add at least one content source',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  FILE_UPLOADED: 'File uploaded successfully',
  CONTENT_ADDED: 'Content added successfully',
  OUTLINE_GENERATED: 'Podcast outline generated successfully',
  SCRIPT_GENERATED: 'Podcast script generated successfully',
  AUDIO_GENERATED: 'Podcast audio generated successfully',
  PODCAST_SAVED: 'Podcast saved to history',
} as const;
