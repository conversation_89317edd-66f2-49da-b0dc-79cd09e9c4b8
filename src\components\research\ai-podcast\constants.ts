import { GenerationSettings, PodcastStyle, PodcastLength } from './types';

// AI Models for podcast generation
export const AI_MODELS = {
  GEMINI_PRO: 'gemini-2.5-pro',
  GEMINI_FLASH: 'gemini-2.5-flash',
} as const;

// Default generation settings
export const DEFAULT_GENERATION_SETTINGS: GenerationSettings = {
  model: AI_MODELS.GEMINI_PRO,
  voiceSettings: {
    hostVoice: 'default-host',
    guestVoice: 'default-guest',
    speed: 1.0,
    pitch: 1.0,
  },
  style: PodcastStyle.CONVERSATIONAL,
  length: PodcastLength.MEDIUM,
  language: 'en',
};

// Supported file types for upload
export const SUPPORTED_FILE_TYPES = {
  PDF: '.pdf',
  DOC: '.doc,.docx',
  TXT: '.txt',
  RTF: '.rtf',
} as const;

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_TEXT_LENGTH = 50000; // 50k characters
export const MAX_SOURCES = 5; // Maximum number of content sources

// Podcast styles with descriptions
export const PODCAST_STYLES = [
  {
    id: PodcastStyle.CONVERSATIONAL,
    name: 'Conversational',
    description: 'Natural, friendly discussion between two people',
    icon: '💬',
  },
  {
    id: PodcastStyle.INTERVIEW,
    name: 'Interview',
    description: 'Structured Q&A format with host asking questions',
    icon: '🎤',
  },
  {
    id: PodcastStyle.EDUCATIONAL,
    name: 'Educational',
    description: 'Informative content with clear explanations',
    icon: '📚',
  },
  {
    id: PodcastStyle.DEBATE,
    name: 'Debate',
    description: 'Discussion of different viewpoints and perspectives',
    icon: '⚖️',
  },
  {
    id: PodcastStyle.STORYTELLING,
    name: 'Storytelling',
    description: 'Narrative format with engaging storytelling elements',
    icon: '📖',
  },
] as const;

// Podcast lengths with descriptions
export const PODCAST_LENGTHS = [
  {
    id: PodcastLength.SHORT,
    name: 'Short',
    description: '3-5 minutes',
    duration: '3-5 min',
    wordCount: '400-600 words',
  },
  {
    id: PodcastLength.MEDIUM,
    name: 'Medium',
    description: '8-12 minutes',
    duration: '8-12 min',
    wordCount: '1000-1500 words',
  },
  {
    id: PodcastLength.LONG,
    name: 'Long',
    description: '15-20 minutes',
    duration: '15-20 min',
    wordCount: '2000-3000 words',
  },
] as const;

// Voice options (these would be populated from TTS service)
export const VOICE_OPTIONS = {
  HOST_VOICES: [
    { id: 'host-1', name: 'Professional Male', preview: '/audio/host-1-preview.mp3' },
    { id: 'host-2', name: 'Professional Female', preview: '/audio/host-2-preview.mp3' },
    { id: 'host-3', name: 'Casual Male', preview: '/audio/host-3-preview.mp3' },
    { id: 'host-4', name: 'Casual Female', preview: '/audio/host-4-preview.mp3' },
  ],
  GUEST_VOICES: [
    { id: 'guest-1', name: 'Expert Male', preview: '/audio/guest-1-preview.mp3' },
    { id: 'guest-2', name: 'Expert Female', preview: '/audio/guest-2-preview.mp3' },
    { id: 'guest-3', name: 'Friendly Male', preview: '/audio/guest-3-preview.mp3' },
    { id: 'guest-4', name: 'Friendly Female', preview: '/audio/guest-4-preview.mp3' },
  ],
} as const;

// Generation prompts
export const GENERATION_PROMPTS = {
  OUTLINE: `
    Analyze the provided content and create a detailed podcast outline. 
    
    Requirements:
    - Create an engaging title that captures the main topic
    - Develop a structured outline with clear sections
    - Identify 3-5 key discussion points
    - Estimate total duration based on content depth
    - Ensure the outline flows naturally for a {style} style podcast
    - Target length: {length}
    
    Return the response in JSON format:
    {
      "title": "Engaging podcast title",
      "outline": "Detailed markdown outline",
      "keyPoints": ["point1", "point2", "point3"],
      "estimatedDuration": 600
    }
  `,
  
  SCRIPT: `
    Convert the provided outline and content into a natural podcast script for two speakers.
    
    Requirements:
    - Create engaging dialogue between a host and guest
    - Use conversational, natural language (avoid formal academic tone)
    - Include smooth transitions between topics
    - Maintain {style} style throughout
    - Target approximately {wordCount} words for {duration} duration
    - Host should guide the conversation and ask questions
    - Guest should provide detailed insights and explanations
    - Include natural speech patterns (pauses, emphasis, etc.)
    
    Return the response in JSON format:
    {
      "script": [
        {"speaker": "host", "text": "Welcome to today's discussion about...", "order": 1},
        {"speaker": "guest", "text": "Thanks for having me. This topic is fascinating because...", "order": 2}
      ],
      "metadata": {
        "wordCount": 1200,
        "estimatedDuration": 720,
        "speakerBalance": {"host": 40, "guest": 60}
      }
    }
  `,
} as const;

// Error messages
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: `File size exceeds ${MAX_FILE_SIZE / 1024 / 1024}MB limit`,
  UNSUPPORTED_FILE_TYPE: 'Unsupported file type. Please upload PDF, DOC, or TXT files.',
  TEXT_TOO_LONG: `Text content exceeds ${MAX_TEXT_LENGTH} character limit`,
  TOO_MANY_SOURCES: `Maximum ${MAX_SOURCES} content sources allowed`,
  GENERATION_FAILED: 'Failed to generate podcast. Please try again.',
  UPLOAD_FAILED: 'Failed to upload file. Please try again.',
  INVALID_URL: 'Please enter a valid URL',
  NO_CONTENT: 'Please add at least one content source',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  FILE_UPLOADED: 'File uploaded successfully',
  CONTENT_ADDED: 'Content added successfully',
  OUTLINE_GENERATED: 'Podcast outline generated successfully',
  SCRIPT_GENERATED: 'Podcast script generated successfully',
  AUDIO_GENERATED: 'Podcast audio generated successfully',
  PODCAST_SAVED: 'Podcast saved to history',
} as const;
