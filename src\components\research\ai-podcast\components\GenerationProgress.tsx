import React from 'react';
import { GenerationStep } from '../types';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  FileText, 
  MessageSquare, 
  Mic, 
  CheckCircle, 
  AlertCircle,
  Loader2
} from 'lucide-react';

interface GenerationProgressProps {
  step: GenerationStep;
  error?: string;
}

export function GenerationProgress({ step, error }: GenerationProgressProps) {
  const steps = [
    {
      id: GenerationStep.OUTLINE,
      label: 'Generating Outline',
      description: 'Analyzing content and creating podcast structure',
      icon: FileText,
    },
    {
      id: GenerationStep.SCRIPT,
      label: 'Writing Script',
      description: 'Creating conversational dialogue between speakers',
      icon: MessageSquare,
    },
    {
      id: GenerationStep.AUDIO,
      label: 'Generating Audio',
      description: 'Converting script to natural speech',
      icon: Mic,
    },
  ];

  const getCurrentStepIndex = () => {
    return steps.findIndex(s => s.id === step);
  };

  const getStepStatus = (stepIndex: number) => {
    const currentIndex = getCurrentStepIndex();
    if (error) {
      return stepIndex <= currentIndex ? 'error' : 'pending';
    }
    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'active';
    return 'pending';
  };

  const getProgressPercentage = () => {
    if (error) return 0;
    const currentIndex = getCurrentStepIndex();
    return ((currentIndex + 1) / steps.length) * 100;
  };

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Overall Progress */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-medium text-gray-900">
                {error ? 'Generation Failed' : 'Generating Podcast'}
              </h3>
              <span className="text-sm text-gray-600">
                {error ? '0%' : `${Math.round(getProgressPercentage())}%`}
              </span>
            </div>
            <Progress 
              value={getProgressPercentage()} 
              className={`h-2 ${error ? 'bg-red-100' : ''}`}
            />
          </div>

          {/* Error Message */}
          {error && (
            <div className="flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-red-900">Generation Error</h4>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          )}

          {/* Step Progress */}
          <div className="space-y-4">
            {steps.map((stepItem, index) => {
              const status = getStepStatus(index);
              const Icon = stepItem.icon;
              
              return (
                <div key={stepItem.id} className="flex items-start gap-4">
                  {/* Step Icon */}
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                    ${status === 'completed' 
                      ? 'bg-green-100 border-green-500 text-green-600' 
                      : status === 'active'
                      ? 'bg-blue-100 border-blue-500 text-blue-600'
                      : status === 'error'
                      ? 'bg-red-100 border-red-500 text-red-600'
                      : 'bg-gray-100 border-gray-300 text-gray-400'
                    }
                  `}>
                    {status === 'completed' ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : status === 'active' ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : status === 'error' ? (
                      <AlertCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>

                  {/* Step Content */}
                  <div className="flex-1 min-w-0">
                    <h4 className={`
                      font-medium transition-colors
                      ${status === 'completed' 
                        ? 'text-green-900' 
                        : status === 'active'
                        ? 'text-blue-900'
                        : status === 'error'
                        ? 'text-red-900'
                        : 'text-gray-500'
                      }
                    `}>
                      {stepItem.label}
                    </h4>
                    <p className={`
                      text-sm mt-1 transition-colors
                      ${status === 'completed' 
                        ? 'text-green-700' 
                        : status === 'active'
                        ? 'text-blue-700'
                        : status === 'error'
                        ? 'text-red-700'
                        : 'text-gray-500'
                      }
                    `}>
                      {stepItem.description}
                    </p>

                    {/* Active Step Animation */}
                    {status === 'active' && !error && (
                      <div className="mt-2">
                        <div className="flex items-center gap-2 text-xs text-blue-600">
                          <div className="flex space-x-1">
                            <div className="w-1 h-1 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                            <div className="w-1 h-1 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                            <div className="w-1 h-1 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                          </div>
                          <span>Processing...</span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Step Number */}
                  <div className={`
                    text-xs font-medium px-2 py-1 rounded-full
                    ${status === 'completed' 
                      ? 'bg-green-100 text-green-800' 
                      : status === 'active'
                      ? 'bg-blue-100 text-blue-800'
                      : status === 'error'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-gray-100 text-gray-600'
                    }
                  `}>
                    {index + 1}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Completion Message */}
          {step === GenerationStep.COMPLETE && !error && (
            <div className="flex items-start gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-green-900">Podcast Generated Successfully!</h4>
                <p className="text-sm text-green-700 mt-1">
                  Your podcast is ready to play. You can find it in the Player tab.
                </p>
              </div>
            </div>
          )}

          {/* Estimated Time */}
          {!error && step !== GenerationStep.COMPLETE && (
            <div className="text-center">
              <p className="text-sm text-gray-600">
                Estimated time remaining: {getEstimatedTime(step)}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function getEstimatedTime(step: GenerationStep): string {
  switch (step) {
    case GenerationStep.OUTLINE:
      return '30-60 seconds';
    case GenerationStep.SCRIPT:
      return '1-2 minutes';
    case GenerationStep.AUDIO:
      return '2-3 minutes';
    default:
      return 'A few moments';
  }
}
