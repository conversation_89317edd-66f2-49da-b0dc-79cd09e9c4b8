import { supabase } from '@/lib/supabase';
import { 
  GeneratedPodcast, 
  PodcastRecord, 
  ContentSourceRecord,
  ContentSource 
} from '../types';

class SupabasePodcastService {
  private readonly PODCASTS_TABLE = 'ai_podcasts';
  private readonly SOURCES_TABLE = 'podcast_sources';

  async savePodcast(podcast: GeneratedPodcast): Promise<string> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Prepare podcast record
      const podcastRecord: Omit<PodcastRecord, 'created_at' | 'updated_at'> = {
        id: podcast.id,
        user_id: user.id,
        title: podcast.metadata.title,
        description: podcast.metadata.description,
        script_data: {
          segments: podcast.script.segments,
          wordCount: podcast.script.wordCount,
          totalDuration: podcast.script.totalDuration,
        },
        audio_url: podcast.audioUrl,
        status: podcast.status,
        metadata: {
          language: podcast.metadata.language,
          tags: podcast.metadata.tags,
          duration: podcast.metadata.duration,
        },
        sources: podcast.sources.map(source => ({
          id: source.id,
          type: source.type,
          name: source.name,
          size: source.size,
        })),
      };

      // Insert podcast record
      const { data: podcastData, error: podcastError } = await supabase
        .from(this.PODCASTS_TABLE)
        .insert(podcastRecord)
        .select()
        .single();

      if (podcastError) {
        throw new Error(`Failed to save podcast: ${podcastError.message}`);
      }

      // Save content sources
      const sourceRecords: Omit<ContentSourceRecord, 'created_at'>[] = podcast.sources.map(source => ({
        id: source.id,
        podcast_id: podcast.id,
        type: source.type,
        name: source.name,
        content: source.content,
        file_url: undefined, // Would be set if file was uploaded to storage
        extracted_text: source.extractedText,
      }));

      if (sourceRecords.length > 0) {
        const { error: sourcesError } = await supabase
          .from(this.SOURCES_TABLE)
          .insert(sourceRecords);

        if (sourcesError) {
          console.error('Failed to save content sources:', sourcesError);
          // Don't throw here as the main podcast was saved successfully
        }
      }

      return podcast.id;

    } catch (error) {
      console.error('Error saving podcast:', error);
      throw new Error(`Failed to save podcast: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getUserPodcasts(limit: number = 50, offset: number = 0): Promise<GeneratedPodcast[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data: podcastRecords, error } = await supabase
        .from(this.PODCASTS_TABLE)
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw new Error(`Failed to fetch podcasts: ${error.message}`);
      }

      if (!podcastRecords) {
        return [];
      }

      // Convert records to GeneratedPodcast objects
      const podcasts: GeneratedPodcast[] = await Promise.all(
        podcastRecords.map(async (record) => {
          // Fetch content sources for this podcast
          const sources = await this.getPodcastSources(record.id);
          
          return {
            id: record.id,
            metadata: {
              title: record.title,
              description: record.description,
              language: record.metadata?.language || 'en',
              tags: record.metadata?.tags || [],
              duration: record.metadata?.duration,
            },
            script: {
              id: `script_${record.id}`,
              segments: record.script_data?.segments || [],
              wordCount: record.script_data?.wordCount || 0,
              totalDuration: record.script_data?.totalDuration,
            },
            audioUrl: record.audio_url,
            status: record.status,
            createdAt: new Date(record.created_at),
            updatedAt: new Date(record.updated_at),
            sources: sources,
            generationSettings: {
              model: 'gemini-2.5-pro', // Default fallback
              voiceSettings: {
                hostVoice: 'default-host',
                guestVoice: 'default-guest',
                speed: 1.0,
                pitch: 1.0,
              },
              style: 'conversational' as any,
              length: 'medium' as any,
              language: record.metadata?.language || 'en',
            },
          };
        })
      );

      return podcasts;

    } catch (error) {
      console.error('Error fetching user podcasts:', error);
      throw new Error(`Failed to fetch podcasts: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getPodcast(podcastId: string): Promise<GeneratedPodcast | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data: record, error } = await supabase
        .from(this.PODCASTS_TABLE)
        .select('*')
        .eq('id', podcastId)
        .eq('user_id', user.id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        throw new Error(`Failed to fetch podcast: ${error.message}`);
      }

      if (!record) {
        return null;
      }

      // Fetch content sources
      const sources = await this.getPodcastSources(record.id);

      return {
        id: record.id,
        metadata: {
          title: record.title,
          description: record.description,
          language: record.metadata?.language || 'en',
          tags: record.metadata?.tags || [],
          duration: record.metadata?.duration,
        },
        script: {
          id: `script_${record.id}`,
          segments: record.script_data?.segments || [],
          wordCount: record.script_data?.wordCount || 0,
          totalDuration: record.script_data?.totalDuration,
        },
        audioUrl: record.audio_url,
        status: record.status,
        createdAt: new Date(record.created_at),
        updatedAt: new Date(record.updated_at),
        sources: sources,
        generationSettings: {
          model: 'gemini-2.5-pro',
          voiceSettings: {
            hostVoice: 'default-host',
            guestVoice: 'default-guest',
            speed: 1.0,
            pitch: 1.0,
          },
          style: 'conversational' as any,
          length: 'medium' as any,
          language: record.metadata?.language || 'en',
        },
      };

    } catch (error) {
      console.error('Error fetching podcast:', error);
      throw new Error(`Failed to fetch podcast: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async deletePodcast(podcastId: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Delete content sources first
      const { error: sourcesError } = await supabase
        .from(this.SOURCES_TABLE)
        .delete()
        .eq('podcast_id', podcastId);

      if (sourcesError) {
        console.error('Failed to delete content sources:', sourcesError);
      }

      // Delete podcast record
      const { error: podcastError } = await supabase
        .from(this.PODCASTS_TABLE)
        .delete()
        .eq('id', podcastId)
        .eq('user_id', user.id);

      if (podcastError) {
        throw new Error(`Failed to delete podcast: ${podcastError.message}`);
      }

    } catch (error) {
      console.error('Error deleting podcast:', error);
      throw new Error(`Failed to delete podcast: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async updatePodcastStatus(podcastId: string, status: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase
        .from(this.PODCASTS_TABLE)
        .update({ 
          status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', podcastId)
        .eq('user_id', user.id);

      if (error) {
        throw new Error(`Failed to update podcast status: ${error.message}`);
      }

    } catch (error) {
      console.error('Error updating podcast status:', error);
      throw new Error(`Failed to update podcast status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async getPodcastSources(podcastId: string): Promise<ContentSource[]> {
    try {
      const { data: sourceRecords, error } = await supabase
        .from(this.SOURCES_TABLE)
        .select('*')
        .eq('podcast_id', podcastId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Failed to fetch content sources:', error);
        return [];
      }

      if (!sourceRecords) {
        return [];
      }

      return sourceRecords.map(record => ({
        id: record.id,
        type: record.type as any,
        name: record.name,
        content: record.content,
        uploadedAt: new Date(record.created_at),
        extractedText: record.extracted_text,
      }));

    } catch (error) {
      console.error('Error fetching content sources:', error);
      return [];
    }
  }

  // Utility method to check if tables exist and create them if needed
  async initializeTables(): Promise<void> {
    try {
      // This would typically be handled by Supabase migrations
      // but we can check if tables exist here
      console.log('Checking podcast tables...');
      
      // In a real implementation, you would run SQL migrations here
      // For now, we assume the tables exist
      
    } catch (error) {
      console.error('Error initializing tables:', error);
    }
  }
}

export const supabasePodcastService = new SupabasePodcastService();
