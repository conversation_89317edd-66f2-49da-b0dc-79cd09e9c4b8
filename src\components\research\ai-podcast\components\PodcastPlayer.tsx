import React, { useState, useRef, useEffect } from 'react';
import { GeneratedPodcast, PodcastSegment } from '../types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward,
  Volume2,
  Download,
  Share2,
  FileText,
  Clock,
  User,
  Users
} from 'lucide-react';

interface PodcastPlayerProps {
  podcast: GeneratedPodcast;
}

export function PodcastPlayer({ podcast }: PodcastPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [currentSegmentIndex, setCurrentSegmentIndex] = useState(0);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Simulate audio playback for demo (in real implementation, use actual audio)
  useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        setCurrentTime(prev => {
          const newTime = prev + 1;
          if (newTime >= duration) {
            setIsPlaying(false);
            return 0;
          }
          return newTime;
        });
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isPlaying, duration]);

  // Set initial duration based on script
  useEffect(() => {
    if (podcast.script.totalDuration) {
      setDuration(podcast.script.totalDuration);
    } else {
      // Estimate duration based on word count (average 150 words per minute)
      const estimatedDuration = Math.round((podcast.script.wordCount / 150) * 60);
      setDuration(estimatedDuration);
    }
  }, [podcast]);

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (newTime: number) => {
    setCurrentTime(newTime);
  };

  const handleSkipBack = () => {
    setCurrentTime(Math.max(0, currentTime - 10));
  };

  const handleSkipForward = () => {
    setCurrentTime(Math.min(duration, currentTime + 10));
  };

  const handleDownload = () => {
    // In real implementation, trigger audio file download
    console.log('Download podcast:', podcast.id);
  };

  const handleShare = () => {
    // In real implementation, open share dialog
    console.log('Share podcast:', podcast.id);
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getCurrentSegment = (): PodcastSegment | null => {
    // Simple implementation - in real app, calculate based on actual timestamps
    return podcast.script.segments[currentSegmentIndex] || null;
  };

  const getProgressPercentage = (): number => {
    return duration > 0 ? (currentTime / duration) * 100 : 0;
  };

  return (
    <div className="space-y-6">
      {/* Podcast Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <CardTitle className="text-2xl">{podcast.metadata.title}</CardTitle>
              <p className="text-gray-600">{podcast.metadata.description}</p>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {formatTime(duration)}
                </div>
                <div className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  {podcast.script.wordCount} words
                </div>
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  {podcast.script.segments.length} segments
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                {podcast.metadata.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">{tag}</Badge>
                ))}
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Audio Player */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Progress Bar */}
            <div className="space-y-2">
              <Progress value={getProgressPercentage()} className="h-2" />
              <div className="flex justify-between text-sm text-gray-500">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>

            {/* Controls */}
            <div className="flex items-center justify-center gap-4">
              <Button variant="outline" size="sm" onClick={handleSkipBack}>
                <SkipBack className="h-4 w-4" />
              </Button>
              <Button size="lg" onClick={handlePlayPause} className="rounded-full w-12 h-12">
                {isPlaying ? (
                  <Pause className="h-6 w-6" />
                ) : (
                  <Play className="h-6 w-6" />
                )}
              </Button>
              <Button variant="outline" size="sm" onClick={handleSkipForward}>
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>

            {/* Volume Control */}
            <div className="flex items-center justify-center gap-2">
              <Volume2 className="h-4 w-4 text-gray-500" />
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={(e) => setVolume(parseFloat(e.target.value))}
                className="w-24"
              />
            </div>

            {/* Current Segment */}
            {getCurrentSegment() && (
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <User className="h-4 w-4" />
                  <span className="font-medium capitalize">
                    {getCurrentSegment()?.speaker}
                  </span>
                </div>
                <p className="text-gray-700 italic">
                  "{getCurrentSegment()?.text}"
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Podcast Content */}
      <Tabs defaultValue="script" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="script">Script</TabsTrigger>
          <TabsTrigger value="sources">Sources</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="script">
          <Card>
            <CardHeader>
              <CardTitle>Podcast Script</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {podcast.script.segments.map((segment, index) => (
                    <div
                      key={segment.id}
                      className={`p-4 rounded-lg border-l-4 ${
                        segment.speaker === 'host'
                          ? 'border-l-blue-500 bg-blue-50'
                          : 'border-l-green-500 bg-green-50'
                      } ${index === currentSegmentIndex ? 'ring-2 ring-blue-300' : ''}`}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <User className="h-4 w-4" />
                        <span className="font-medium capitalize text-sm">
                          {segment.speaker}
                        </span>
                        <span className="text-xs text-gray-500">
                          Segment {segment.order}
                        </span>
                      </div>
                      <p className="text-gray-700 leading-relaxed">
                        {segment.text}
                      </p>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sources">
          <Card>
            <CardHeader>
              <CardTitle>Content Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {podcast.sources.map((source) => (
                  <div key={source.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-medium">{source.name}</h4>
                        <p className="text-sm text-gray-600 mt-1">
                          {source.type.toUpperCase()} • {source.content.length} characters
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Added {source.uploadedAt.toLocaleString()}
                        </p>
                      </div>
                      <Badge variant="outline">{source.type}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Generation Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">AI Model</label>
                  <p className="text-sm text-gray-600">{podcast.generationSettings.model}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Style</label>
                  <p className="text-sm text-gray-600 capitalize">{podcast.generationSettings.style}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Length</label>
                  <p className="text-sm text-gray-600 capitalize">{podcast.generationSettings.length}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Language</label>
                  <p className="text-sm text-gray-600">{podcast.generationSettings.language}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
