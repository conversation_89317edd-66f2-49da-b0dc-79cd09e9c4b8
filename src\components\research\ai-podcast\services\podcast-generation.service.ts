import { GoogleGenerativeAI } from '@google/generative-ai';
import { 
  ContentSource, 
  GenerationSettings, 
  OutlineGenerationResponse, 
  ScriptGenerationResponse,
  PodcastSegment
} from '../types';
import { GENERATION_PROMPTS, PODCAST_LENGTHS } from '../constants';

class PodcastGenerationService {
  private genAI: GoogleGenerativeAI;

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('VITE_GEMINI_API_KEY is not configured');
    }
    this.genAI = new GoogleGenerativeAI(apiKey);
  }

  async generateOutline(
    sources: ContentSource[], 
    settings: GenerationSettings
  ): Promise<OutlineGenerationResponse> {
    try {
      const model = this.genAI.getGenerativeModel({ model: settings.model });
      
      // Combine all source content
      const combinedContent = sources
        .map(source => `Source: ${source.name}\nContent: ${source.content}`)
        .join('\n\n---\n\n');

      // Get length configuration
      const lengthConfig = PODCAST_LENGTHS.find(l => l.id === settings.length);
      
      const prompt = GENERATION_PROMPTS.OUTLINE
        .replace('{style}', settings.style)
        .replace('{length}', lengthConfig?.name || 'medium');

      const fullPrompt = `${prompt}\n\nContent to analyze:\n${combinedContent}`;

      const result = await model.generateContent(fullPrompt);
      const response = await result.response;
      const text = response.text();

      // Parse JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Invalid response format from AI model');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      
      // Validate response structure
      if (!parsedResponse.title || !parsedResponse.outline || !parsedResponse.keyPoints) {
        throw new Error('Incomplete outline response from AI model');
      }

      return {
        title: parsedResponse.title,
        outline: parsedResponse.outline,
        keyPoints: Array.isArray(parsedResponse.keyPoints) 
          ? parsedResponse.keyPoints 
          : [parsedResponse.keyPoints],
        estimatedDuration: parsedResponse.estimatedDuration || this.estimateDuration(settings.length),
      };

    } catch (error) {
      console.error('Outline generation failed:', error);
      throw new Error(`Failed to generate outline: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async generateScript(
    outline: OutlineGenerationResponse,
    sources: ContentSource[],
    settings: GenerationSettings
  ): Promise<ScriptGenerationResponse> {
    try {
      const model = this.genAI.getGenerativeModel({ model: settings.model });
      
      // Get length configuration
      const lengthConfig = PODCAST_LENGTHS.find(l => l.id === settings.length);
      const wordCount = this.getTargetWordCount(settings.length);
      
      // Combine outline and source content
      const combinedContent = `
        PODCAST OUTLINE:
        Title: ${outline.title}
        ${outline.outline}
        
        KEY POINTS TO COVER:
        ${outline.keyPoints.map(point => `- ${point}`).join('\n')}
        
        SOURCE CONTENT:
        ${sources.map(source => `Source: ${source.name}\nContent: ${source.content}`).join('\n\n---\n\n')}
      `;

      const prompt = GENERATION_PROMPTS.SCRIPT
        .replace('{style}', settings.style)
        .replace('{wordCount}', wordCount.toString())
        .replace('{duration}', `${Math.round(outline.estimatedDuration / 60)} minutes`);

      const fullPrompt = `${prompt}\n\nContent to convert to script:\n${combinedContent}`;

      const result = await model.generateContent(fullPrompt);
      const response = await result.response;
      const text = response.text();

      // Parse JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Invalid response format from AI model');
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      
      // Validate response structure
      if (!parsedResponse.script || !Array.isArray(parsedResponse.script)) {
        throw new Error('Invalid script format from AI model');
      }

      // Process and validate script segments
      const processedSegments: PodcastSegment[] = parsedResponse.script.map((segment: any, index: number) => ({
        id: `segment_${Date.now()}_${index}`,
        speaker: segment.speaker === 'host' ? 'host' : 'guest',
        text: segment.text || '',
        order: segment.order || index + 1,
        duration: this.estimateSegmentDuration(segment.text || ''),
      }));

      // Calculate metadata
      const totalWordCount = processedSegments.reduce(
        (count, segment) => count + this.countWords(segment.text), 
        0
      );
      
      const totalDuration = processedSegments.reduce(
        (duration, segment) => duration + (segment.duration || 0), 
        0
      );

      const speakerBalance = this.calculateSpeakerBalance(processedSegments);

      return {
        script: processedSegments,
        metadata: {
          wordCount: totalWordCount,
          estimatedDuration: totalDuration,
          speakerBalance,
        },
      };

    } catch (error) {
      console.error('Script generation failed:', error);
      throw new Error(`Failed to generate script: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async generateAudio(
    script: PodcastSegment[],
    settings: GenerationSettings
  ): Promise<string> {
    // Placeholder for audio generation
    // In a real implementation, this would integrate with a TTS service
    // like Google Cloud Text-to-Speech, ElevenLabs, or similar
    
    try {
      // Simulate audio generation delay
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Return a placeholder audio URL
      // In real implementation, this would be the actual generated audio file URL
      return '/audio/generated-podcast-placeholder.mp3';
      
    } catch (error) {
      console.error('Audio generation failed:', error);
      throw new Error(`Failed to generate audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private estimateDuration(length: string): number {
    // Estimate duration in seconds based on length setting
    switch (length) {
      case 'short':
        return 240; // 4 minutes
      case 'medium':
        return 600; // 10 minutes
      case 'long':
        return 1080; // 18 minutes
      default:
        return 600;
    }
  }

  private getTargetWordCount(length: string): number {
    // Target word count based on length (assuming ~150 words per minute)
    switch (length) {
      case 'short':
        return 500;
      case 'medium':
        return 1250;
      case 'long':
        return 2250;
      default:
        return 1250;
    }
  }

  private estimateSegmentDuration(text: string): number {
    // Estimate segment duration based on word count (150 words per minute)
    const wordCount = this.countWords(text);
    return Math.round((wordCount / 150) * 60);
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  private calculateSpeakerBalance(segments: PodcastSegment[]): { host: number; guest: number } {
    const hostWords = segments
      .filter(s => s.speaker === 'host')
      .reduce((count, s) => count + this.countWords(s.text), 0);
    
    const guestWords = segments
      .filter(s => s.speaker === 'guest')
      .reduce((count, s) => count + this.countWords(s.text), 0);
    
    const totalWords = hostWords + guestWords;
    
    if (totalWords === 0) {
      return { host: 50, guest: 50 };
    }
    
    return {
      host: Math.round((hostWords / totalWords) * 100),
      guest: Math.round((guestWords / totalWords) * 100),
    };
  }

  // Utility method to validate content before generation
  validateSources(sources: ContentSource[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (sources.length === 0) {
      errors.push('At least one content source is required');
    }
    
    const totalContentLength = sources.reduce((total, source) => total + source.content.length, 0);
    if (totalContentLength < 100) {
      errors.push('Content is too short for meaningful podcast generation');
    }
    
    if (totalContentLength > 100000) {
      errors.push('Content is too long and may result in generation errors');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Method to get generation cost estimate (for future pricing features)
  estimateGenerationCost(sources: ContentSource[], settings: GenerationSettings): number {
    const totalTokens = sources.reduce((total, source) => total + Math.ceil(source.content.length / 4), 0);
    const modelMultiplier = settings.model.includes('pro') ? 2 : 1;
    
    // Placeholder cost calculation (in credits or currency units)
    return Math.ceil(totalTokens * 0.001 * modelMultiplier);
  }
}

export const podcastGenerationService = new PodcastGenerationService();
