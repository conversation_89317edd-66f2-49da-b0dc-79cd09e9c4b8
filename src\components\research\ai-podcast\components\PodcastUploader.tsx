import React, { useRef, useState } from 'react';
import { ContentSource, ContentType } from '../types';
import { SUPPORTED_FILE_TYPES, MAX_FILE_SIZE } from '../constants';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Upload, File, X, CheckCircle, AlertCircle } from 'lucide-react';

interface PodcastUploaderProps {
  onFileUploaded: (source: ContentSource) => void;
  disabled?: boolean;
}

export function PodcastUploader({ onFileUploaded, disabled = false }: PodcastUploaderProps) {
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      setUploadError(validation.error || 'Invalid file');
      return;
    }

    setIsUploading(true);
    setUploadError(null);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      for (let i = 0; i <= 100; i += 10) {
        setUploadProgress(i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Extract text content
      const content = await extractTextFromFile(file);
      
      const source: ContentSource = {
        id: `file_${Date.now()}`,
        type: getFileType(file.name),
        name: file.name,
        content: content,
        size: file.size,
        uploadedAt: new Date(),
        extractedText: content,
      };

      onFileUploaded(source);
      
      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (error) {
      setUploadError(error instanceof Error ? error.message : 'Upload failed');
    } finally {
      setIsUploading(false);
      setTimeout(() => {
        setUploadProgress(0);
        setUploadError(null);
      }, 2000);
    }
  };

  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    if (file.size > MAX_FILE_SIZE) {
      return { isValid: false, error: `File too large. Maximum size is ${MAX_FILE_SIZE / 1024 / 1024}MB` };
    }

    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    const supportedTypes = Object.values(SUPPORTED_FILE_TYPES).join(',');
    if (!supportedTypes.includes(extension)) {
      return { isValid: false, error: 'Unsupported file type' };
    }

    return { isValid: true };
  };

  const extractTextFromFile = async (file: File): Promise<string> => {
    // Simplified text extraction - in real implementation, use proper libraries
    if (file.type === 'text/plain') {
      return await file.text();
    }
    
    // For other file types, you would use appropriate libraries:
    // - PDF: pdf-parse, pdf2pic
    // - DOC/DOCX: mammoth, docx-parser
    // - For now, return placeholder
    return `Extracted content from ${file.name} (${file.size} bytes)`;
  };

  const getFileType = (fileName: string): ContentType => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'document';
      default:
        return 'document';
    }
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div 
          className={`
            border-2 border-dashed rounded-lg p-8 text-center transition-colors
            ${disabled 
              ? 'border-gray-200 bg-gray-50' 
              : 'border-gray-300 hover:border-gray-400 cursor-pointer'
            }
          `}
          onClick={() => !disabled && fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept={Object.values(SUPPORTED_FILE_TYPES).join(',')}
            onChange={handleFileSelect}
            disabled={disabled || isUploading}
            className="hidden"
          />

          {isUploading ? (
            <div className="space-y-4">
              <Upload className="h-12 w-12 text-blue-500 mx-auto animate-pulse" />
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Uploading...</h3>
                <Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
                <p className="text-sm text-gray-600 mt-2">{uploadProgress}% complete</p>
              </div>
            </div>
          ) : uploadError ? (
            <div className="space-y-4">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
              <div>
                <h3 className="text-lg font-medium text-red-900 mb-2">Upload Failed</h3>
                <p className="text-sm text-red-600">{uploadError}</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-3"
                  onClick={() => setUploadError(null)}
                >
                  Try Again
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Upload className="h-12 w-12 text-gray-400 mx-auto" />
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {disabled ? 'Upload Disabled' : 'Upload Documents'}
                </h3>
                <p className="text-gray-600 mb-4">
                  Support for PDF, DOC, DOCX, and TXT files up to {MAX_FILE_SIZE / 1024 / 1024}MB
                </p>
                <Button 
                  variant="outline" 
                  disabled={disabled}
                  onClick={(e) => {
                    e.stopPropagation();
                    fileInputRef.current?.click();
                  }}
                >
                  Choose File
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* File Type Info */}
        <div className="mt-4 text-xs text-gray-500 text-center">
          <p>Supported formats: PDF, DOC, DOCX, TXT</p>
          <p>Maximum file size: {MAX_FILE_SIZE / 1024 / 1024}MB</p>
        </div>
      </CardContent>
    </Card>
  );
}
