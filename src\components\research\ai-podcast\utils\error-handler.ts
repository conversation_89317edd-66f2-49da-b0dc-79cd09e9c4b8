import { toast } from 'sonner';

export class PodcastError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'PodcastError';
  }
}

export class ValidationError extends PodcastError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

export class GenerationError extends PodcastError {
  constructor(message: string, details?: any) {
    super(message, 'GENERATION_ERROR', details);
    this.name = 'GenerationError';
  }
}

export class UploadError extends PodcastError {
  constructor(message: string, details?: any) {
    super(message, 'UPLOAD_ERROR', details);
    this.name = 'UploadError';
  }
}

export class DatabaseError extends PodcastError {
  constructor(message: string, details?: any) {
    super(message, 'DATABASE_ERROR', details);
    this.name = 'DatabaseError';
  }
}

export function handleError(error: unknown, context?: string): void {
  console.error(`Error in ${context || 'unknown context'}:`, error);

  if (error instanceof ValidationError) {
    toast.error(`Validation Error: ${error.message}`);
  } else if (error instanceof GenerationError) {
    toast.error(`Generation Failed: ${error.message}`);
  } else if (error instanceof UploadError) {
    toast.error(`Upload Failed: ${error.message}`);
  } else if (error instanceof DatabaseError) {
    toast.error(`Database Error: ${error.message}`);
  } else if (error instanceof PodcastError) {
    toast.error(`Podcast Error: ${error.message}`);
  } else if (error instanceof Error) {
    toast.error(`Error: ${error.message}`);
  } else {
    toast.error('An unexpected error occurred');
  }
}

export function validateContentSources(sources: any[]): void {
  if (!Array.isArray(sources)) {
    throw new ValidationError('Sources must be an array');
  }

  if (sources.length === 0) {
    throw new ValidationError('At least one content source is required');
  }

  if (sources.length > 5) {
    throw new ValidationError('Maximum 5 content sources allowed');
  }

  sources.forEach((source, index) => {
    if (!source.id || !source.type || !source.name || !source.content) {
      throw new ValidationError(`Invalid source at index ${index}: missing required fields`);
    }

    if (typeof source.content !== 'string') {
      throw new ValidationError(`Invalid source at index ${index}: content must be a string`);
    }

    if (source.content.length < 10) {
      throw new ValidationError(`Invalid source at index ${index}: content too short`);
    }

    if (source.content.length > 50000) {
      throw new ValidationError(`Invalid source at index ${index}: content too long`);
    }
  });
}

export function validateGenerationSettings(settings: any): void {
  if (!settings || typeof settings !== 'object') {
    throw new ValidationError('Generation settings are required');
  }

  const requiredFields = ['model', 'style', 'length', 'language', 'voiceSettings'];
  for (const field of requiredFields) {
    if (!settings[field]) {
      throw new ValidationError(`Missing required setting: ${field}`);
    }
  }

  if (!settings.voiceSettings.hostVoice || !settings.voiceSettings.guestVoice) {
    throw new ValidationError('Both host and guest voices must be selected');
  }

  if (typeof settings.voiceSettings.speed !== 'number' || 
      settings.voiceSettings.speed < 0.5 || 
      settings.voiceSettings.speed > 2.0) {
    throw new ValidationError('Voice speed must be between 0.5 and 2.0');
  }

  if (typeof settings.voiceSettings.pitch !== 'number' || 
      settings.voiceSettings.pitch < 0.5 || 
      settings.voiceSettings.pitch > 2.0) {
    throw new ValidationError('Voice pitch must be between 0.5 and 2.0');
  }
}

export function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  return new Promise((resolve, reject) => {
    let retries = 0;

    const attempt = async () => {
      try {
        const result = await fn();
        resolve(result);
      } catch (error) {
        retries++;
        
        if (retries >= maxRetries) {
          reject(error);
          return;
        }

        const delay = baseDelay * Math.pow(2, retries - 1);
        console.log(`Attempt ${retries} failed, retrying in ${delay}ms...`);
        
        setTimeout(attempt, delay);
      }
    };

    attempt();
  });
}

export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/\s+/g, ' ') // Normalize whitespace
    .substring(0, 50000); // Limit length
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
