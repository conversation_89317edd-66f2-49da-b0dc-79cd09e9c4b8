import React, { useState, useRef } from 'react';
import { toast } from 'sonner';
import { ContentSource, ContentType, UploadProgress } from '../types';
import { 
  SUPPORTED_FILE_TYPES, 
  MAX_FILE_SIZE, 
  MAX_TEXT_LENGTH, 
  MAX_SOURCES,
  ERROR_MESSAGES 
} from '../constants';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Upload, 
  FileText, 
  Link, 
  X, 
  File,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';

interface ContentInputProps {
  sources: ContentSource[];
  onAddSource: (source: ContentSource) => void;
  onRemoveSource: (sourceId: string) => void;
  disabled?: boolean;
}

export function ContentInput({ 
  sources, 
  onAddSource, 
  onRemoveSource, 
  disabled = false 
}: ContentInputProps) {
  const [textInput, setTextInput] = useState('');
  const [urlInput, setUrlInput] = useState('');
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const canAddMore = sources.length < MAX_SOURCES;

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      toast.error(ERROR_MESSAGES.FILE_TOO_LARGE);
      return;
    }

    // Validate file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    const supportedTypes = Object.values(SUPPORTED_FILE_TYPES).join(',');
    if (!supportedTypes.includes(fileExtension)) {
      toast.error(ERROR_MESSAGES.UNSUPPORTED_FILE_TYPE);
      return;
    }

    if (!canAddMore) {
      toast.error(ERROR_MESSAGES.TOO_MANY_SOURCES);
      return;
    }

    setUploadProgress({
      fileName: file.name,
      progress: 0,
      status: 'uploading',
    });

    try {
      // Simulate upload progress
      for (let i = 0; i <= 100; i += 10) {
        setUploadProgress(prev => prev ? { ...prev, progress: i } : null);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      setUploadProgress(prev => prev ? { ...prev, status: 'processing' } : null);

      // Extract text from file (simplified - in real implementation, use proper text extraction)
      const text = await extractTextFromFile(file);
      
      if (text.length > MAX_TEXT_LENGTH) {
        toast.error(ERROR_MESSAGES.TEXT_TOO_LONG);
        setUploadProgress(null);
        return;
      }

      const source: ContentSource = {
        id: `file_${Date.now()}`,
        type: getFileType(file.name),
        name: file.name,
        content: text,
        size: file.size,
        uploadedAt: new Date(),
        extractedText: text,
      };

      setUploadProgress(prev => prev ? { ...prev, status: 'completed' } : null);
      onAddSource(source);
      
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      
      setTimeout(() => setUploadProgress(null), 2000);
      
    } catch (error) {
      console.error('File upload failed:', error);
      setUploadProgress(prev => prev ? { ...prev, status: 'error', error: 'Upload failed' } : null);
      toast.error(ERROR_MESSAGES.UPLOAD_FAILED);
      setTimeout(() => setUploadProgress(null), 3000);
    }
  };

  const handleTextSubmit = () => {
    if (!textInput.trim()) {
      toast.error('Please enter some text content');
      return;
    }

    if (textInput.length > MAX_TEXT_LENGTH) {
      toast.error(ERROR_MESSAGES.TEXT_TOO_LONG);
      return;
    }

    if (!canAddMore) {
      toast.error(ERROR_MESSAGES.TOO_MANY_SOURCES);
      return;
    }

    const source: ContentSource = {
      id: `text_${Date.now()}`,
      type: 'text',
      name: `Text Content (${textInput.length} chars)`,
      content: textInput,
      uploadedAt: new Date(),
    };

    onAddSource(source);
    setTextInput('');
    toast.success('Text content added successfully');
  };

  const handleUrlSubmit = async () => {
    if (!urlInput.trim()) {
      toast.error('Please enter a URL');
      return;
    }

    if (!isValidUrl(urlInput)) {
      toast.error(ERROR_MESSAGES.INVALID_URL);
      return;
    }

    if (!canAddMore) {
      toast.error(ERROR_MESSAGES.TOO_MANY_SOURCES);
      return;
    }

    setIsProcessing(true);
    try {
      // In a real implementation, you would fetch and extract content from the URL
      const content = await fetchUrlContent(urlInput);
      
      if (content.length > MAX_TEXT_LENGTH) {
        toast.error(ERROR_MESSAGES.TEXT_TOO_LONG);
        return;
      }

      const source: ContentSource = {
        id: `url_${Date.now()}`,
        type: 'url',
        name: urlInput,
        content: content,
        uploadedAt: new Date(),
      };

      onAddSource(source);
      setUrlInput('');
      toast.success('URL content added successfully');
      
    } catch (error) {
      console.error('URL processing failed:', error);
      toast.error('Failed to process URL content');
    } finally {
      setIsProcessing(false);
    }
  };

  const extractTextFromFile = async (file: File): Promise<string> => {
    // Simplified text extraction - in real implementation, use proper libraries
    if (file.type === 'text/plain') {
      return await file.text();
    }
    // For PDFs and other formats, you would use appropriate libraries
    return `Extracted text from ${file.name} (${file.size} bytes)`;
  };

  const getFileType = (fileName: string): ContentType => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'document';
      default:
        return 'document';
    }
  };

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const fetchUrlContent = async (url: string): Promise<string> => {
    // Placeholder implementation - in real app, use a backend service
    return `Content extracted from ${url}`;
  };

  return (
    <div className="space-y-6">
      {/* Upload Progress */}
      {uploadProgress && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-2">
              {uploadProgress.status === 'uploading' && <Loader2 className="h-4 w-4 animate-spin text-blue-600" />}
              {uploadProgress.status === 'processing' && <Loader2 className="h-4 w-4 animate-spin text-blue-600" />}
              {uploadProgress.status === 'completed' && <CheckCircle className="h-4 w-4 text-green-600" />}
              {uploadProgress.status === 'error' && <AlertCircle className="h-4 w-4 text-red-600" />}
              <span className="text-sm font-medium">{uploadProgress.fileName}</span>
            </div>
            <Progress value={uploadProgress.progress} className="h-2" />
            <div className="text-xs text-gray-600 mt-1">
              {uploadProgress.status === 'uploading' && 'Uploading...'}
              {uploadProgress.status === 'processing' && 'Processing content...'}
              {uploadProgress.status === 'completed' && 'Upload completed!'}
              {uploadProgress.status === 'error' && (uploadProgress.error || 'Upload failed')}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Content Input Tabs */}
      <Tabs defaultValue="file" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="file" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Upload File
          </TabsTrigger>
          <TabsTrigger value="text" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Text Input
          </TabsTrigger>
          <TabsTrigger value="url" className="flex items-center gap-2">
            <Link className="h-4 w-4" />
            Web URL
          </TabsTrigger>
        </TabsList>

        <TabsContent value="file" className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Upload Documents</h3>
            <p className="text-gray-600 mb-4">
              Support for PDF, DOC, DOCX, and TXT files up to {MAX_FILE_SIZE / 1024 / 1024}MB
            </p>
            <input
              ref={fileInputRef}
              type="file"
              accept={Object.values(SUPPORTED_FILE_TYPES).join(',')}
              onChange={handleFileUpload}
              disabled={disabled || !canAddMore}
              className="hidden"
            />
            <Button
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || !canAddMore}
              variant="outline"
            >
              Choose File
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="text" className="space-y-4">
          <div>
            <Label htmlFor="text-input">Text Content</Label>
            <Textarea
              id="text-input"
              placeholder="Paste your text content here..."
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              disabled={disabled}
              rows={8}
              className="mt-2"
            />
            <div className="flex justify-between items-center mt-2">
              <span className="text-sm text-gray-500">
                {textInput.length} / {MAX_TEXT_LENGTH} characters
              </span>
              <Button
                onClick={handleTextSubmit}
                disabled={disabled || !textInput.trim() || !canAddMore}
              >
                Add Text
              </Button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="url" className="space-y-4">
          <div>
            <Label htmlFor="url-input">Website URL</Label>
            <div className="flex gap-2 mt-2">
              <Input
                id="url-input"
                type="url"
                placeholder="https://example.com/article"
                value={urlInput}
                onChange={(e) => setUrlInput(e.target.value)}
                disabled={disabled || isProcessing}
                className="flex-1"
              />
              <Button
                onClick={handleUrlSubmit}
                disabled={disabled || !urlInput.trim() || isProcessing || !canAddMore}
              >
                {isProcessing ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  'Add URL'
                )}
              </Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Sources List */}
      {sources.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-medium">Content Sources ({sources.length}/{MAX_SOURCES})</h3>
          {sources.map((source) => (
            <Card key={source.id} className="border-l-4 border-l-blue-500">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <File className="h-5 w-5 text-gray-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-gray-900">{source.name}</h4>
                      <p className="text-sm text-gray-600">
                        {source.type.toUpperCase()} • {source.content.length} characters
                        {source.size && ` • ${(source.size / 1024).toFixed(1)} KB`}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Added {source.uploadedAt.toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemoveSource(source.id)}
                    disabled={disabled}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {!canAddMore && (
        <div className="text-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">
            Maximum of {MAX_SOURCES} content sources reached. Remove a source to add more.
          </p>
        </div>
      )}
    </div>
  );
}
