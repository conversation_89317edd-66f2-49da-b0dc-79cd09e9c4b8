# AI Podcast Generator Module

Transform your research content into engaging podcasts with AI-powered conversation generation.

## Features

### 🎙️ Content-to-Podcast Conversion
- Upload PDFs, documents, and text content
- Add web URLs for automatic content extraction
- Support for multiple content sources per podcast
- Intelligent content analysis and structuring

### 🤖 AI-Powered Generation
- **Outline Generation**: Creates structured podcast outlines from your content
- **Script Writing**: Generates natural conversational dialogue between host and guest
- **Multiple Styles**: Conversational, Interview, Educational, Debate, Storytelling
- **Customizable Length**: Short (3-5 min), Medium (8-12 min), Long (15-20 min)

### 🎵 Audio Features
- Text-to-speech conversion (placeholder implementation)
- Multiple voice options for host and guest
- Adjustable speech speed and pitch
- Audio player with playback controls

### 📚 Content Management
- Podcast history and library
- Search and filter capabilities
- Export functionality
- Supabase integration for data persistence

## Architecture

### Components Structure
```
ai-podcast/
├── AIPodcastGenerator.tsx          # Main component
├── components/
│   ├── ContentInput.tsx            # File upload and text input
│   ├── GenerationProgress.tsx      # Progress tracking
│   ├── PodcastPlayer.tsx          # Audio playback
│   ├── PodcastHistory.tsx         # History management
│   ├── SettingsPanel.tsx          # Configuration
│   └── PodcastUploader.tsx        # File upload utility
├── services/
│   ├── podcast-generation.service.ts  # AI generation logic
│   └── supabase-podcast.service.ts    # Database operations
├── utils/
│   └── error-handler.ts           # Error handling utilities
├── types.ts                       # TypeScript interfaces
├── constants.ts                   # Configuration constants
└── supabase-schema.sql           # Database schema
```

### Key Services

#### PodcastGenerationService
- Integrates with Google Gemini API
- Handles outline and script generation
- Validates content and settings
- Estimates generation costs

#### SupabasePodcastService
- Manages podcast data persistence
- Handles user authentication
- Implements row-level security
- Provides CRUD operations

## Usage

### Basic Workflow
1. **Upload Content**: Add PDFs, documents, or text content
2. **Configure Settings**: Choose style, length, voices, and AI model
3. **Generate Outline**: AI creates structured podcast outline
4. **Generate Script**: AI writes conversational dialogue
5. **Generate Audio**: Convert script to speech (placeholder)
6. **Play & Export**: Listen to podcast and download

### Content Sources
- **PDF Documents**: Research papers, reports, articles
- **Text Input**: Direct text content, notes, summaries
- **Web URLs**: Articles, blog posts, web content
- **Document Files**: DOC, DOCX, TXT files

### Podcast Styles
- **Conversational**: Natural, friendly discussion
- **Interview**: Structured Q&A format
- **Educational**: Informative with clear explanations
- **Debate**: Multiple viewpoints and perspectives
- **Storytelling**: Narrative format with engaging elements

## Configuration

### Environment Variables
```env
VITE_GEMINI_API_KEY=your_gemini_api_key
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Supabase Setup
1. Run the SQL schema in `supabase-schema.sql`
2. Enable Row Level Security
3. Configure authentication policies

### AI Model Configuration
- **Gemini 2.5 Pro**: Best quality, slower generation
- **Gemini 2.5 Flash**: Faster generation, good quality

## Integration

### Adding to Research Platform
1. Import in `ResearchDashboard.tsx`:
```typescript
import { AIPodcastGenerator } from "./ai-podcast";
```

2. Add to ActiveView type:
```typescript
export type ActiveView = "ai-podcast" | /* other views */;
```

3. Add routing:
```typescript
{activeView === "ai-podcast" && <AIPodcastGenerator />}
```

4. Update Sidebar navigation:
```typescript
{ id: "ai-podcast", label: "AI Podcast Generator", icon: Mic }
```

## Error Handling

### Custom Error Types
- `ValidationError`: Input validation failures
- `GenerationError`: AI generation failures
- `UploadError`: File upload issues
- `DatabaseError`: Supabase operation failures

### Error Recovery
- Automatic retry with exponential backoff
- User-friendly error messages
- Graceful degradation for non-critical features

## Performance Considerations

### Content Limits
- Maximum 5 content sources per podcast
- 50,000 character limit per source
- 10MB file size limit for uploads

### Generation Optimization
- Content validation before generation
- Progress tracking for long operations
- Efficient token usage for AI models

## Future Enhancements

### Audio Generation
- Integration with ElevenLabs or Google Cloud TTS
- Voice cloning capabilities
- Background music and sound effects

### Advanced Features
- Multi-language support
- Custom voice training
- Podcast series generation
- Social sharing integration

### Analytics
- Generation metrics
- Usage statistics
- Quality feedback system

## Dependencies

### Core Dependencies
- `@google/generative-ai`: Google Gemini API
- `@supabase/supabase-js`: Database operations
- `sonner`: Toast notifications
- `lucide-react`: Icons

### UI Components
- Radix UI primitives
- Tailwind CSS styling
- Custom component library

## Testing

### Unit Tests
- Service layer testing
- Component testing with React Testing Library
- Error handling validation

### Integration Tests
- End-to-end podcast generation
- Supabase integration testing
- API error scenarios

## Contributing

### Development Setup
1. Install dependencies: `npm install`
2. Configure environment variables
3. Set up Supabase database
4. Run development server: `npm run dev`

### Code Style
- TypeScript strict mode
- ESLint configuration
- Prettier formatting
- Component documentation

## License

This module is part of the Paper Genius research platform and follows the same licensing terms.
