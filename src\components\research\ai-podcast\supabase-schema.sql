-- AI Podcast Generator Tables
-- Run this SQL in your Supabase SQL editor to create the required tables

-- Table for storing generated podcasts
CREATE TABLE IF NOT EXISTS ai_podcasts (
    id TEXT PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    script_data JSONB NOT NULL DEFAULT '{}',
    audio_url TEXT,
    status TEXT NOT NULL DEFAULT 'draft',
    metadata JSONB DEFAULT '{}',
    sources JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing podcast content sources
CREATE TABLE IF NOT EXISTS podcast_sources (
    id TEXT PRIMARY KEY,
    podcast_id TEXT REFERENCES ai_podcasts(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    name TEXT NOT NULL,
    content TEXT NOT NULL,
    file_url TEXT,
    extracted_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ai_podcasts_user_id ON ai_podcasts(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_podcasts_created_at ON ai_podcasts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_podcasts_status ON ai_podcasts(status);
CREATE INDEX IF NOT EXISTS idx_podcast_sources_podcast_id ON podcast_sources(podcast_id);

-- Row Level Security (RLS) policies
ALTER TABLE ai_podcasts ENABLE ROW LEVEL SECURITY;
ALTER TABLE podcast_sources ENABLE ROW LEVEL SECURITY;

-- Policy for ai_podcasts table
CREATE POLICY "Users can view their own podcasts" ON ai_podcasts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own podcasts" ON ai_podcasts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own podcasts" ON ai_podcasts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own podcasts" ON ai_podcasts
    FOR DELETE USING (auth.uid() = user_id);

-- Policy for podcast_sources table
CREATE POLICY "Users can view sources of their own podcasts" ON podcast_sources
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM ai_podcasts 
            WHERE ai_podcasts.id = podcast_sources.podcast_id 
            AND ai_podcasts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert sources for their own podcasts" ON podcast_sources
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM ai_podcasts 
            WHERE ai_podcasts.id = podcast_sources.podcast_id 
            AND ai_podcasts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update sources of their own podcasts" ON podcast_sources
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM ai_podcasts 
            WHERE ai_podcasts.id = podcast_sources.podcast_id 
            AND ai_podcasts.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete sources of their own podcasts" ON podcast_sources
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM ai_podcasts 
            WHERE ai_podcasts.id = podcast_sources.podcast_id 
            AND ai_podcasts.user_id = auth.uid()
        )
    );

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at on ai_podcasts
CREATE TRIGGER update_ai_podcasts_updated_at 
    BEFORE UPDATE ON ai_podcasts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Optional: Create a view for easier querying of podcasts with source counts
CREATE OR REPLACE VIEW podcast_summary AS
SELECT 
    p.*,
    COUNT(s.id) as source_count,
    ARRAY_AGG(s.type) FILTER (WHERE s.type IS NOT NULL) as source_types
FROM ai_podcasts p
LEFT JOIN podcast_sources s ON p.id = s.podcast_id
GROUP BY p.id, p.user_id, p.title, p.description, p.script_data, 
         p.audio_url, p.status, p.metadata, p.sources, p.created_at, p.updated_at;
