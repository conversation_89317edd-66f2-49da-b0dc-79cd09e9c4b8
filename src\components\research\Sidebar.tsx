import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  FileText,
  Search,
  BookOpen,
  MessageSquare,
  Settings,
  Plus,
  GraduationCap,
  FolderOpen,
  Star,
  Clock,
  Bot,
  Sparkles,
  Book,
  FileCheck,
  FileEdit,
  BarChart3,
  Brain,
  Home,
  DollarSign,
  FileQuestion,
  Users,
  Phone,
  LogOut,
  Import,
  History,
  Globe,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  Database as DatabaseIcon,
  GitBranch,
  Image,
  Presentation,
  Workflow,
  TrendingUp,
  Target,
  Wand2
} from "lucide-react";
import { ActiveView } from "./ResearchDashboard";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { DocumentManager } from "./DocumentManager";
import { useState, useEffect } from "react";
import { documentService } from "@/services/documentService";
import { Database } from "@/lib/database.types";

type Document = Database['public']['Tables']['user_documents']['Row'];

interface SidebarProps {
  activeView: ActiveView;
  onViewChange: (view: ActiveView) => void;
  onOpenDocument?: (document: Document) => void;
  selectedDocumentId?: string;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

export function Sidebar({
  activeView,
  onViewChange,
  onOpenDocument,
  selectedDocumentId,
  collapsed = false,
  onToggleCollapse
}: SidebarProps) {
  const navigate = useNavigate();
  const { user, signOut } = useAuth();
  const [showDocumentManager, setShowDocumentManager] = useState(activeView === 'editor');
  
  // Toggle document manager when editor is selected
  useEffect(() => {
    setShowDocumentManager(activeView === 'editor');
  }, [activeView]);

  const handleNewDocument = () => {
    // Always go to editor and let it handle document creation
    onViewChange('editor');
  };

  const handleImportDocument = () => {
    // Always go to editor and let it handle document import
    onViewChange('editor');
  };

  const handleOpenDocument = (document: Document) => {
    if (onOpenDocument) {
      onOpenDocument(document);
    }
    onViewChange('editor');
  };
  
  const handleSignOut = async () => {
    try {
      console.log("Initiating sign out from Sidebar");
      
      // Call the signOut function from auth context
      const { error } = await signOut();
      
      if (error) {
        console.error("Error during sign out:", error);
        // Even if there's an error, try to clear the session and redirect
        localStorage.removeItem('supabase.auth.token');
        window.location.href = "/login";
        return;
      }
      
      // The redirect will happen inside the signOut function,
      // but just in case, we'll add a failsafe redirect
      setTimeout(() => {
        console.log("Failsafe redirect to login page");
        window.location.href = "/login";
      }, 1000);
    } catch (error) {
      console.error("Unexpected error during sign out:", error);
      // Force redirect to login even if there's an error
      window.location.href = "/login";
    }
  };
  
  const menuItems = [
    { id: "welcome" as ActiveView, label: "AI Dashboard", icon: Home },
    { id: "editor" as ActiveView, label: "Editor", icon: FileText },
    { id: "ai-generator" as ActiveView, label: "AI Paper Generator", icon: Sparkles },
    { id: "book-generator" as ActiveView, label: "AI Book Generator", icon: Book },
    { id: "book-library" as ActiveView, label: "Book Library", icon: History },
    { id: "ai-tutor" as ActiveView, label: "AI Tutor", icon: GraduationCap },
    { id: "ai-humanizer" as ActiveView, label: "Academic Writing Humanizer", icon: Wand2 },
    { id: "article-reviewer" as ActiveView, label: "AI Article Reviewer", icon: FileCheck },
    { id: "article-revision" as ActiveView, label: "AI Article Revision", icon: FileEdit },
    { id: "research-analysis" as ActiveView, label: "Research Analysis", icon: BarChart3 },
    { id: "data-visualization" as ActiveView, label: "Data Visualization", icon: DatabaseIcon },
    { id: "figure-analysis" as ActiveView, label: "AI Figure Analysis", icon: Image },
    { id: "career-explorer" as ActiveView, label: "Career Explorer", icon: TrendingUp },
    { id: "article-finder" as ActiveView, label: "Article Finder", icon: Target },
    { id: "flow-builder" as ActiveView, label: "Flow Chart Builder", icon: GitBranch },
    { id: "flowchart-fun" as ActiveView, label: "Flowchart Fun", icon: Workflow },
    { id: "presentation-generator" as ActiveView, label: "AI Presentation Generator", icon: Presentation },
    { id: "open-deep-research" as ActiveView, label: "Open Deep Research", icon: Brain },
    { id: "research-search" as ActiveView, label: "Research Search", icon: Globe },
    { id: "google-search" as ActiveView, label: "Google Search", icon: Brain },
    { id: "search" as ActiveView, label: "Literature", icon: Search },
    { id: "citations" as ActiveView, label: "Citations", icon: BookOpen },
    { id: "chat" as ActiveView, label: "AI Chat", icon: MessageSquare },
  ];

  const recentDocuments = [
    "Neural Networks in Climate Research",
    "Machine Learning Applications",
    "Research Methodology Review",
    "Data Analysis Framework"
  ];

  return (
    <div className={`${collapsed ? 'w-16' : 'w-80'} bg-white border-r border-gray-200 flex flex-col h-full transition-all duration-300 ease-in-out relative`}>
      {/* Toggle Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={onToggleCollapse}
        className="absolute -right-3 top-6 z-10 h-6 w-6 p-0 rounded-full bg-white border border-gray-200 shadow-md hover:shadow-lg transition-all duration-200"
      >
        {collapsed ? <ChevronRight className="h-3 w-3" /> : <ChevronLeft className="h-3 w-3" />}
      </Button>

      {/* Header */}
      <div className="p-4 border-b bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="flex items-center mb-4">
          <GraduationCap className="h-8 w-8 text-blue-600 mr-2" />
          {!collapsed && (
            <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Verbira
            </span>
          )}
        </div>
        {!collapsed && (
          <Button
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            size="sm"
            onClick={handleNewDocument}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Document
          </Button>
        )}
      </div>

      {/* Toggle between AI Tools and Document Manager */}
      {!collapsed && (
        <div className="border-b">
          <div className="flex">
            <Button
              variant={!showDocumentManager ? "default" : "ghost"}
              className="flex-1 rounded-none"
              onClick={() => setShowDocumentManager(false)}
            >
              <Bot className="h-4 w-4 mr-2" />
              AI Tools
            </Button>
            <Button
              variant={showDocumentManager ? "default" : "ghost"}
              className="flex-1 rounded-none"
              onClick={() => setShowDocumentManager(true)}
            >
              <FolderOpen className="h-4 w-4 mr-2" />
              Documents
            </Button>
          </div>
        </div>
      )}

      {/* Collapsed state icons */}
      {collapsed && (
        <div className="border-b p-2">
          <div className="flex flex-col space-y-2">
            <Button
              variant={!showDocumentManager ? "default" : "ghost"}
              size="sm"
              className="w-full p-2"
              onClick={() => setShowDocumentManager(false)}
              title="AI Tools"
            >
              <Bot className="h-4 w-4" />
            </Button>
            <Button
              variant={showDocumentManager ? "default" : "ghost"}
              size="sm"
              className="w-full p-2"
              onClick={() => setShowDocumentManager(true)}
              title="Documents"
            >
              <FolderOpen className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Content Area */}
      <div className="flex-1 overflow-hidden">
        {showDocumentManager ? (
          collapsed ? (
            <div className="p-2">
              <div className="text-center text-xs text-gray-500">Docs</div>
            </div>
          ) : (
            <DocumentManager
              onOpenDocument={handleOpenDocument}
              onNewDocument={handleNewDocument}
              onImportDocument={handleImportDocument}
              selectedDocumentId={selectedDocumentId}
            />
          )
        ) : (
          <div className="h-full flex flex-col">
            {/* AI Tools Navigation */}
            <ScrollArea className="flex-1">
              <nav className={`${collapsed ? 'p-2' : 'p-4'} space-y-2`}>
                {!collapsed && (
                  <div className="mb-4">
                    <h3 className="text-xs font-medium text-gray-500 mb-2 uppercase tracking-wider">AI Research Tools</h3>
                  </div>
                )}
                {menuItems.map((item) => (
                  <Button
                    key={item.id}
                    variant={activeView === item.id ? "secondary" : "ghost"}
                    className={`w-full ${collapsed ? 'justify-center p-2' : 'justify-start'}`}
                    onClick={() => onViewChange(item.id)}
                    title={collapsed ? item.label : undefined}
                  >
                    <item.icon className={`h-4 w-4 ${collapsed ? '' : 'mr-3'}`} />
                    {!collapsed && item.label}
                  </Button>
                ))}
              </nav>
            </ScrollArea>
          </div>
        )}
      </div>

      {/* Quick Links Icons */}
      <div className="p-3 border-t bg-gradient-to-r from-blue-50 to-purple-50">
        {collapsed ? (
          <div className="flex flex-col space-y-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full hover:bg-blue-100 hover:scale-110 transition-all duration-200 group"
              onClick={() => navigate("/")}
              title="Home"
            >
              <Home className="h-3 w-3 text-blue-600 group-hover:text-blue-700" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full hover:bg-green-100 hover:scale-110 transition-all duration-200 group"
              onClick={() => navigate("/pricing")}
              title="Pricing"
            >
              <DollarSign className="h-3 w-3 text-green-600 group-hover:text-green-700" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full hover:bg-purple-100 hover:scale-110 transition-all duration-200 group"
              onClick={() => navigate("/docs")}
              title="Documentation"
            >
              <FileQuestion className="h-3 w-3 text-purple-600 group-hover:text-purple-700" />
            </Button>
          </div>
        ) : (
          <div className="flex justify-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              className="h-10 w-10 p-0 rounded-full hover:bg-blue-100 hover:scale-110 transition-all duration-200 group"
              onClick={() => navigate("/")}
              title="Home"
            >
              <Home className="h-4 w-4 text-blue-600 group-hover:text-blue-700" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-10 w-10 p-0 rounded-full hover:bg-green-100 hover:scale-110 transition-all duration-200 group"
              onClick={() => navigate("/pricing")}
              title="Pricing"
            >
              <DollarSign className="h-4 w-4 text-green-600 group-hover:text-green-700" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-10 w-10 p-0 rounded-full hover:bg-purple-100 hover:scale-110 transition-all duration-200 group"
              onClick={() => navigate("/docs")}
              title="Documentation"
            >
              <FileQuestion className="h-4 w-4 text-purple-600 group-hover:text-purple-700" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-10 w-10 p-0 rounded-full hover:bg-indigo-100 hover:scale-110 transition-all duration-200 group"
              onClick={() => navigate("/about")}
              title="About"
            >
              <Users className="h-4 w-4 text-indigo-600 group-hover:text-indigo-700" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-10 w-10 p-0 rounded-full hover:bg-orange-100 hover:scale-110 transition-all duration-200 group"
              onClick={() => navigate("/contact")}
              title="Contact"
            >
              <Phone className="h-4 w-4 text-orange-600 group-hover:text-orange-700" />
            </Button>
          </div>
        )}
      </div>

      {/* Settings & User */}
      <div className="p-4 border-t bg-gray-50">
        {collapsed ? (
          <div className="flex flex-col space-y-2 items-center">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              title="Settings"
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
              onClick={handleSignOut}
              title="Sign Out"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div className="space-y-2">
            <Button variant="ghost" className="w-full justify-start text-sm">
              <Settings className="h-4 w-4 mr-3" />
              Settings
            </Button>
            {user && (
              <div className="text-xs text-gray-500 px-3 py-2 border-t">
                <div className="truncate">{user.email}</div>
              </div>
            )}
            <Button
              variant="ghost"
              className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 text-sm"
              onClick={handleSignOut}
            >
              <LogOut className="h-4 w-4 mr-3" />
              Sign Out
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
