import React, { useState } from 'react';
import { GeneratedPodcast, PodcastStatus } from '../types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Play, 
  Download, 
  Share2, 
  Trash2, 
  Search,
  RefreshCw,
  Clock,
  FileText,
  Calendar,
  Filter,
  SortAsc,
  SortDesc
} from 'lucide-react';

interface PodcastHistoryProps {
  podcasts: GeneratedPodcast[];
  isLoading: boolean;
  onLoadPodcast: (podcast: GeneratedPodcast) => void;
  onRefresh: () => void;
  onDeletePodcast?: (podcastId: string) => void;
}

export function PodcastHistory({ 
  podcasts, 
  isLoading, 
  onLoadPodcast, 
  onRefresh,
  onDeletePodcast 
}: PodcastHistoryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'title' | 'duration'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterStatus, setFilterStatus] = useState<PodcastStatus | 'all'>('all');

  const filteredAndSortedPodcasts = podcasts
    .filter(podcast => {
      const matchesSearch = podcast.metadata.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           podcast.metadata.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = filterStatus === 'all' || podcast.status === filterStatus;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'title':
          comparison = a.metadata.title.localeCompare(b.metadata.title);
          break;
        case 'duration':
          const aDuration = a.script.totalDuration || 0;
          const bDuration = b.script.totalDuration || 0;
          comparison = aDuration - bDuration;
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  const handleSort = (newSortBy: typeof sortBy) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('desc');
    }
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return 'Unknown';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = (status: PodcastStatus): string => {
    switch (status) {
      case PodcastStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case PodcastStatus.FAILED:
        return 'bg-red-100 text-red-800';
      case PodcastStatus.GENERATING_OUTLINE:
      case PodcastStatus.GENERATING_SCRIPT:
      case PodcastStatus.GENERATING_AUDIO:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Loading podcast history...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Podcast History ({podcasts.length})
            </CardTitle>
            <Button onClick={onRefresh} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search podcasts..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Filter by Status */}
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as PodcastStatus | 'all')}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Status</option>
              <option value={PodcastStatus.COMPLETED}>Completed</option>
              <option value={PodcastStatus.FAILED}>Failed</option>
              <option value={PodcastStatus.DRAFT}>Draft</option>
            </select>

            {/* Sort Controls */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSort('date')}
                className={sortBy === 'date' ? 'bg-blue-50' : ''}
              >
                <Calendar className="h-4 w-4 mr-1" />
                Date
                {sortBy === 'date' && (
                  sortOrder === 'asc' ? <SortAsc className="h-3 w-3 ml-1" /> : <SortDesc className="h-3 w-3 ml-1" />
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSort('title')}
                className={sortBy === 'title' ? 'bg-blue-50' : ''}
              >
                Title
                {sortBy === 'title' && (
                  sortOrder === 'asc' ? <SortAsc className="h-3 w-3 ml-1" /> : <SortDesc className="h-3 w-3 ml-1" />
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSort('duration')}
                className={sortBy === 'duration' ? 'bg-blue-50' : ''}
              >
                <Clock className="h-4 w-4 mr-1" />
                Duration
                {sortBy === 'duration' && (
                  sortOrder === 'asc' ? <SortAsc className="h-3 w-3 ml-1" /> : <SortDesc className="h-3 w-3 ml-1" />
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Podcast List */}
      {filteredAndSortedPodcasts.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery || filterStatus !== 'all' ? 'No matching podcasts' : 'No podcasts yet'}
            </h3>
            <p className="text-gray-600 text-center mb-4">
              {searchQuery || filterStatus !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Create your first podcast to get started'
              }
            </p>
            {!searchQuery && filterStatus === 'all' && (
              <Button onClick={() => window.location.reload()}>
                Create New Podcast
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredAndSortedPodcasts.map((podcast) => (
            <Card key={podcast.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start gap-3">
                      <div className="flex-1">
                        <h3 className="text-lg font-medium text-gray-900 mb-1 truncate">
                          {podcast.metadata.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                          {podcast.metadata.description}
                        </p>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {new Date(podcast.createdAt).toLocaleDateString()}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {formatDuration(podcast.script.totalDuration)}
                          </div>
                          <div className="flex items-center gap-1">
                            <FileText className="h-4 w-4" />
                            {podcast.script.wordCount} words
                          </div>
                        </div>

                        <div className="flex items-center gap-2 mb-3">
                          <Badge className={getStatusColor(podcast.status)}>
                            {podcast.status.replace('_', ' ').toUpperCase()}
                          </Badge>
                          <Badge variant="outline">
                            {podcast.generationSettings.style}
                          </Badge>
                          <Badge variant="outline">
                            {podcast.sources.length} source{podcast.sources.length !== 1 ? 's' : ''}
                          </Badge>
                        </div>

                        {podcast.metadata.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {podcast.metadata.tags.slice(0, 3).map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {podcast.metadata.tags.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{podcast.metadata.tags.length - 3} more
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col gap-2 ml-4">
                    <Button
                      onClick={() => onLoadPodcast(podcast)}
                      disabled={podcast.status !== PodcastStatus.COMPLETED}
                      size="sm"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Play
                    </Button>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Share2 className="h-4 w-4" />
                      </Button>
                      {onDeletePodcast && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => onDeletePodcast(podcast.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
